# Storage Migration Guide: From Browser-Specific to Platform-Independent

This guide documents the migration from browser-specific storage APIs (`localStorage`, `localforage`) to a platform-independent storage abstraction layer.

## Migration Overview

### What Changed

1. **Removed Direct Browser Dependencies**: No more direct `localStorage` or `localforage` usage
2. **Added Storage Abstraction**: Platform-independent storage layer with adapters
3. **Enhanced Repository Pattern**: Base repository class with built-in storage management
4. **Automatic Platform Detection**: Runtime detection of environment (browser, Node.js, React Native)
5. **Built-in Migration Support**: Automatic migration from existing localStorage data

### Benefits

- ✅ **Platform Independence**: Code works in browser, Node.js, and React Native
- ✅ **Consistent API**: Same interface across all platforms
- ✅ **Better Testing**: Memory adapter for unit tests
- ✅ **TTL Support**: Built-in expiration across all platforms
- ✅ **Health Monitoring**: Storage health checks and cleanup
- ✅ **Backward Compatibility**: Automatic migration from localStorage

## Files Modified

### Core Storage Infrastructure

| File | Status | Description |
|------|--------|-------------|
| `src/lib/storage/` | ✅ **NEW** | Complete storage abstraction layer |
| `src/lib/storage/index.ts` | ✅ **NEW** | Main storage API exports |
| `src/lib/storage/IStorageAdapter.ts` | ✅ **NEW** | Storage interface and base implementation |
| `src/lib/storage/StorageManager.ts` | ✅ **NEW** | Central storage management |
| `src/lib/storage/StorageFactory.ts` | ✅ **NEW** | Platform-specific adapter creation |
| `src/lib/storage/platformDetection.ts` | ✅ **NEW** | Runtime platform detection |

### Storage Adapters

| File | Status | Description |
|------|--------|-------------|
| `src/lib/storage/adapters/BrowserStorageAdapter.ts` | ✅ **NEW** | Browser storage (localforage) |
| `src/lib/storage/adapters/NodeStorageAdapter.ts` | ✅ **NEW** | Node.js file system storage |
| `src/lib/storage/adapters/ReactNativeStorageAdapter.ts` | ✅ **NEW** | React Native AsyncStorage |
| `src/lib/storage/adapters/MemoryStorageAdapter.ts` | ✅ **NEW** | In-memory storage for testing |

### Repository Layer

| File | Status | Description |
|------|--------|-------------|
| `src/app/repository/RepositoryBase.ts` | ✅ **UPDATED** | Enhanced with storage abstraction |
| `src/app/repository/AuthRepository.ts` | ✅ **UPDATED** | Uses new storage system |
| `src/app/repository/SessionStorageRepository.ts` | ✅ **UPDATED** | Uses new storage system |
| `src/app/repository/PreferencesRepository.ts` | ✅ **NEW** | Language preferences with migration |

### Application Integration

| File | Status | Description |
|------|--------|-------------|
| `src/app/services/serviceSetup.ts` | ✅ **UPDATED** | Initializes storage system |
| `src/app/ClientProviders.tsx` | ✅ **UPDATED** | Uses PreferencesRepository |
| `src/app/components/Header/LanguageSwitcher.tsx` | ✅ **UPDATED** | Uses PreferencesRepository |
| `src/app/components/Header/MobileMoreMenu.tsx` | ✅ **UPDATED** | Uses PreferencesRepository |

### Testing

| File | Status | Description |
|------|--------|-------------|
| `src/lib/storage/__tests__/StorageAbstraction.test.ts` | ✅ **NEW** | Comprehensive storage tests |
| `src/app/repository/__tests__/RepositoryBase.test.ts` | ✅ **NEW** | Repository base class tests |
| `src/app/repository/__tests__/AuthRepository.test.ts` | ✅ **NEW** | Auth repository tests |

## Migration Details

### 1. Storage Initialization

**Before:**
```typescript
// No explicit initialization needed
localStorage.setItem('key', 'value');
```

**After:**
```typescript
// Initialize storage system at app startup
import { initializeStorage } from '@/lib/storage';

await initializeStorage({
    name: 'infinityAppDB',
    version: 1,
    description: 'Infinity Application Storage'
});
```

**Location**: `src/app/services/serviceSetup.ts` and `src/app/ClientProviders.tsx`

### 2. Repository Pattern Enhancement

**Before:**
```typescript
class AuthRepository {
    async getAccessToken(): Promise<string | null> {
        return localStorage.getItem('access_token');
    }
    
    async setAccessToken(token: string): Promise<void> {
        localStorage.setItem('access_token', token);
    }
}
```

**After:**
```typescript
import { RepositoryBase } from './RepositoryBase';

class AuthRepository extends RepositoryBase {
    constructor() {
        super('auth_store'); // Storage namespace
    }
    
    async getAccessToken(): Promise<string | null> {
        return await this.getItem('access_token');
    }
    
    async setAccessToken(token: string): Promise<void> {
        await this.setItem('access_token', token);
    }
}
```

### 3. Language Preferences Migration

**Before:**
```typescript
// Direct localStorage usage
const savedLang = localStorage.getItem('preferredLanguage');
if (savedLang) {
    i18n.changeLanguage(savedLang);
}

// Saving language
localStorage.setItem('preferredLanguage', language.code);
```

**After:**
```typescript
// Using PreferencesRepository with automatic migration
import { PreferencesRepository } from '@/app/repository/PreferencesRepository';

const preferencesRepository = PreferencesRepository.getInstance();

// Migrate from localStorage if needed (automatic)
await preferencesRepository.migrateLanguageFromLocalStorage('preferredLanguage');

// Load preferred language
const savedLang = await preferencesRepository.loadPreferredLanguage();
if (savedLang) {
    i18n.changeLanguage(savedLang);
}

// Save language preference
await preferencesRepository.savePreferredLanguage(language.code);
```

### 4. Session Storage Migration

**Before:**
```typescript
// Direct localStorage usage for sessions
const sessions = JSON.parse(localStorage.getItem('sessions') || '[]');
const activeSessionId = localStorage.getItem('activeSessionId');
```

**After:**
```typescript
// Using SessionStorageRepository with automatic migration
import { SessionStorageRepository } from '@/app/repository/SessionStorageRepository';

const sessionRepo = SessionStorageRepository.getInstance();

// Migration happens automatically on first access
const sessions = await sessionRepo.loadSessions();
const activeSessionId = await sessionRepo.getActiveSessionId();
```

## Automatic Migration Features

### 1. Language Preferences Migration

The `PreferencesRepository` automatically migrates language preferences from localStorage:

```typescript
// Automatically called during initialization
await preferencesRepository.migrateLanguageFromLocalStorage('preferredLanguage');
```

**Migration Process:**
1. Check if new storage already has language preference
2. If not, look for old localStorage key
3. Migrate data to new storage system
4. Clean up old localStorage entry
5. Log migration success

### 2. Session Data Migration

The `SessionStorageRepository` automatically migrates session data:

```typescript
// Automatically called during initialization
await sessionRepo.migrateFromLocalStorage('sessions', 'activeSessionId');
```

**Migration Process:**
1. Check if new storage already has session data
2. If not, look for old localStorage keys
3. Migrate sessions array and active session ID
4. Clean up old localStorage entries
5. Log migration success

## Platform-Specific Behavior

### Browser Environment
- **Storage Backend**: localforage (IndexedDB → localStorage fallback)
- **Migration**: Automatic from localStorage
- **TTL**: Supported with cleanup
- **Max Size**: ~50MB (IndexedDB)

### Node.js Environment
- **Storage Backend**: File system (JSON files)
- **Migration**: N/A (server-side)
- **TTL**: Supported with cleanup
- **Max Size**: Unlimited

### React Native Environment
- **Storage Backend**: AsyncStorage
- **Migration**: From AsyncStorage if applicable
- **TTL**: Supported with cleanup
- **Max Size**: ~6MB

### Testing Environment
- **Storage Backend**: In-memory Map
- **Migration**: N/A (clean state)
- **TTL**: Supported
- **Max Size**: Configurable

## Testing the Migration

### 1. Unit Tests

Run the storage abstraction tests:
```bash
npm test -- --testPathPattern="StorageAbstraction"
npm test -- --testPathPattern="RepositoryBase"
npm test -- --testPathPattern="AuthRepository"
```

### 2. Integration Testing

Test the migration in a browser:

1. **Setup Old Data**: Add data to localStorage manually
   ```javascript
   localStorage.setItem('preferredLanguage', 'ta');
   localStorage.setItem('access_token', 'old_token');
   ```

2. **Load Application**: The migration should happen automatically

3. **Verify Migration**: Check that data is accessible through new repositories
   ```javascript
   // Should return 'ta' (migrated from localStorage)
   const lang = await preferencesRepository.loadPreferredLanguage();
   
   // Should return 'old_token' (migrated from localStorage)
   const token = await authRepository.getAccessToken();
   ```

4. **Verify Cleanup**: Check that old localStorage entries are removed

### 3. Build Testing

Ensure the build works correctly:
```bash
npm run build
```

The build should complete successfully with only warnings (no errors).

## Rollback Plan

If issues arise, the migration can be rolled back by:

1. **Reverting Repository Changes**: Restore direct localStorage usage in repositories
2. **Removing Storage Initialization**: Remove storage initialization from serviceSetup.ts
3. **Restoring Direct Usage**: Restore direct localStorage usage in components

However, the new system is designed to be backward compatible and should not require rollback.

## Performance Impact

### Positive Impacts
- ✅ **Better Caching**: TTL support reduces stale data
- ✅ **Efficient Storage**: IndexedDB for large datasets
- ✅ **Cleanup**: Automatic removal of expired data
- ✅ **Health Monitoring**: Proactive issue detection

### Considerations
- ⚠️ **Async Operations**: All storage operations are now async
- ⚠️ **Initialization**: Requires storage initialization at startup
- ⚠️ **Memory Usage**: Slightly higher due to abstraction layer

## Monitoring and Maintenance

### Health Checks

Monitor storage health in production:
```typescript
import { getStorageHealth } from '@/lib/storage';

const health = await getStorageHealth();
if (!health.healthy) {
    console.error('Storage health issues:', health.adapters);
}
```

### Cleanup Operations

Run periodic cleanup to remove expired items:
```typescript
import { getStorageManager } from '@/lib/storage';

const manager = getStorageManager();
const cleanupResults = await manager.performGlobalCleanup();
console.log('Cleaned up expired items:', cleanupResults);
```

## Support and Troubleshooting

### Common Issues

1. **Storage Not Initialized**: Ensure `initializeStorage()` is called at app startup
2. **Migration Not Working**: Check browser console for migration logs
3. **Platform Detection Issues**: Verify platform detection in different environments
4. **Performance Issues**: Monitor storage health and run cleanup regularly

### Debug Information

Enable debug logging to troubleshoot issues:
```typescript
// Check platform detection
import { detectPlatform } from '@/lib/storage';
console.log('Platform:', detectPlatform());

// Check storage capabilities
const storage = await getRepositoryStorage('debug');
console.log('Capabilities:', storage.getCapabilities());
```

## Conclusion

The migration to platform-independent storage provides:

- **Future-Proofing**: Ready for React Native and server-side rendering
- **Better Testing**: Consistent testing across environments
- **Enhanced Features**: TTL, health monitoring, automatic cleanup
- **Backward Compatibility**: Automatic migration from existing data

The migration is designed to be seamless with automatic data migration and backward compatibility.
