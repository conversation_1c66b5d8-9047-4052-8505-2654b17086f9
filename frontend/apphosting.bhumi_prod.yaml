# Settings for Backend (on Cloud Run).
# See https://firebase.google.com/docs/app-hosting/configure#cloud-run
runConfig:
  minInstances: 1
  # maxInstances: 100
  # concurrency: 80
  # cpu: 1
  # memoryMiB: 512

# Build configuration - exclude admin for customer deployment
buildConfig:
  commands:
    - name: "Install dependencies"
      command: "npm ci"
    - name: "Build customer version (no admin)"
      command: "npm run build:customer"

# Environment variables and secrets.
env:
  - variable: NEXT_PUBLIC_DEFAULT_LANGUAGE
    value: ta
  - variable: NEXT_PUBLIC_APP_MODE
    value: customer
  - variable: NEXT_PUBLIC_API_URL
    value: https://apis.wow-firebase-apps.wheelocity.com/
  - variable: NEXT_PUBLIC_POSTHOG_KEY
    value: phc_sRdr034JgNvWoWUL2cYHggkiR4Fbh0fcVrwCSgB27SC
  - variable: NEXT_PUBLIC_POSTHOG_HOST
    value: https://eu.i.posthog.com
