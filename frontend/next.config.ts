/** @type {import('next').NextConfig} */
import pkg from './package.json' assert { type: 'json' };

const nextConfig = {
  env: {
    NEXT_PUBLIC_VERSION: pkg.version,
  },
  images: {
    domains: ['*'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*',
      },
    ],
  },
  // Remove console.log in production for better performance and security
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn'], // Keep console.error and console.warn in production
    } : false,
  },

  // Webpack configuration to handle React Native modules
  webpack: (config: any, { isServer }: { isServer: boolean }) => {
    // Ignore React Native modules that don't exist in web environment
    config.resolve.fallback = {
      ...config.resolve.fallback,
      'react-native': false,
      '@react-native-async-storage/async-storage': false,
      'react-native-device-info': false,
      'react-native-permissions': false,
      'react-native-camera': false,
      'react-native-geolocation-service': false,
      'react-native-push-notification': false,
    };

    // Ignore React Native package.json
    config.resolve.alias = {
      ...config.resolve.alias,
      'react-native/package.json': false,
    };

    return config;
  },

  // Exclude admin routes from production and test builds
  async redirects() {
    // Only exclude admin in production/test environments
    if (process.env.NEXT_PUBLIC_APP_MODE === 'customer') {
      return [
        {
          source: '/admin/:path*',
          destination: '/404',
          permanent: false,
        },
      ];
    }
    return [];
  },

  // Optional: Also block admin API routes if any exist
  async rewrites() {
    // Only exclude admin API routes in production/test environments
    if (process.env.NEXT_PUBLIC_APP_MODE === 'customer') {
      return [
        {
          source: '/api/admin/:path*',
          destination: '/api/404',
        },
      ];
    }
    return [];
  },
}

module.exports = nextConfig;
