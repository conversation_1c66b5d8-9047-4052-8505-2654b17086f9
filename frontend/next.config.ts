/** @type {import('next').NextConfig} */
import pkg from './package.json' assert { type: 'json' };

const nextConfig = {
  env: {
    NEXT_PUBLIC_VERSION: pkg.version,
  },
  images: {
    domains: ['*'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*',
      },
    ],
  },
  // Remove console.log in production for better performance and security
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn'], // Keep console.error and console.warn in production
    } : false,
  },

  // Exclude admin routes from production and test builds
  async redirects() {
    // Only exclude admin in production/test environments
    if (process.env.NEXT_PUBLIC_APP_MODE === 'customer') {
      return [
        {
          source: '/admin/:path*',
          destination: '/404',
          permanent: false,
        },
      ];
    }
    return [];
  },

  // Optional: Also block admin API routes if any exist
  async rewrites() {
    // Only exclude admin API routes in production/test environments
    if (process.env.NEXT_PUBLIC_APP_MODE === 'customer') {
      return [
        {
          source: '/api/admin/:path*',
          destination: '/api/404',
        },
      ];
    }
    return [];
  },
}

module.exports = nextConfig;
