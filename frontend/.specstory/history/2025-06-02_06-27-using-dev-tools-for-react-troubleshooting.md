<!-- Generated by SpecStory -->

# Using dev tools for React troubleshooting (2025-06-02 11:57:21)

_**User**_

How to use react dev tools and tanstack query dev tools to understand and fix issues

---

_**Assistant**_



---

I'll help you understand how to use React DevTools and TanStack Query DevTools for debugging. Let me first check your project's dependencies to see what's currently installed.

---

Read file: package.json

---

Great! I can see your project is using `@tanstack/react-query` version 5.76.1. Let me provide you with a comprehensive guide on using both React DevTools and TanStack Query DevTools for debugging.

## React DevTools Setup and Usage

### 1. Installation
React DevTools are available as browser extensions:
- **Chrome**: Install from Chrome Web Store ("React Developer Tools")
- **Firefox**: Install from Firefox Add-ons
- **Edge**: Install from Microsoft Edge Add-ons

### 2. Key Features for Debugging

**Component Tree Inspection:**
- Navigate through your component hierarchy
- View props, state, and hooks in real-time
- Search for specific components
- Filter components by name or type

**Profiler:**
- Measure component render performance
- Identify unnecessary re-renders
- Find performance bottlenecks
- See which props/state changes trigger renders

**Tips for Debugging:**
- Use the "eye" icon to highlight components on hover
- Right-click components in DevTools to inspect in Elements panel
- Use the search filter to quickly find components
- Check the "Rendered by" section to trace component origins

## TanStack Query DevTools Setup

Since you're using TanStack Query v5, you'll need to add the DevTools. Let me check if they're already installed and show you how to set them up:

---

<details>
            <summary>Grep search for "@tanstack/react-query-devtools" • **0** files</summary>
        
No results found
</details>

---

The DevTools aren't installed yet. Let me show you how to add them:

---

```bash
cd /Users/<USER>/Projects/infinity/frontend && npm install @tanstack/react-query-devtools --save-dev
```

---

I'll show you how to install and set up the DevTools. First, you'll need to install the TanStack Query DevTools:

```bash
npm install @tanstack/react-query-devtools --save-dev
```

Now, let me check your current Query Client setup to show you where to add the DevTools:

---

<details>
            <summary>Searched codebase "QueryClient createQueryClient react-query" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/app/ClientProviders.tsx` | L47-88 |
| `src/app/ClientProviders.tsx` | L1-33 |
| `src/app/context/SessionContext.tsx` | L1-17 |
| `src/app/admin/orders/page.tsx` | L1-56 |
| `src/app/page.tsx` | L1-38 |
| `src/app/context/SessionContext.tsx` | L40-81 |
| `src/app/admin/categories/page.tsx` | L1-23 |
| `src/app/admin/page.tsx` | L1-46 |
| `src/app/admin/returns/page.tsx` | L46-101 |
| `src/app/components/common/CategoriesList.tsx` | L1-17 |
| `src/app/admin/orders/[id]/page.tsx` | L1-37 |
| `src/app/context/SessionContext.tsx` | L564-594 |
| `src/app/context/AuthContext.tsx` | L1-9 |
| `src/app/components/SearchBar.tsx` | L1-12 |
| `src/app/admin/orders/[id]/page.tsx` | L37-76 |
| `src/app/context/AuthContext.tsx` | L39-74 |
| `src/app/context/SessionContext.tsx` | L379-405 |
| `src/app/product/[id]/page.tsx` | L1-44 |
| `src/app/admin/picker/page.tsx` | L1-57 |
| `src/app/categories/[slug]/page.tsx` | L22-63 |
| `src/app/components/CategorySection.tsx` | L1-12 |
| `src/app/context/SessionContext.tsx` | L214-261 |
| `package.json` | L1-54 |
| `src/app/admin/categories/page.tsx` | L23-68 |
| `src/app/context/SessionContext.tsx` | L17-40 |

</details>

---

Perfect! Now I can see your QueryClient setup. Let me show you how to add and use both React DevTools and TanStack Query DevTools for debugging.

## Setting Up TanStack Query DevTools

First, you'll need to install the DevTools:

```bash
npm install @tanstack/react-query-devtools --save-dev
```

Then, let's add them to your `ClientProviders.tsx`:

---

Read file: src/app/ClientProviders.tsx

---

Now I

---

