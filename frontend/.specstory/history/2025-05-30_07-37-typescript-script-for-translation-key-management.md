<!-- Generated by SpecStory -->

# Typescript script for translation key management (2025-05-30 13:07:27)

_**User**_

I need a typescript script that does the following

1. Search all the .ts or .tsx  files in the  src directory
2. For each file, check the hardcoded string whihc are not using translation.
3. Output a single file .json file with missing keys and it's value. Make sure the strucutre of json remain same as in @en.json 

Additional scripts - one more script that takes two json @en.json and missing.en.json and merge everything. In case of overwrite, the @en.json has high priority so it will override if key conflicts.

Do the followin in order:
- Please first understand, read the necessary code. 
- Let me know what you understood and what is your plan.
- Wait for my approval. DON'T CODE YET.
- AFTER MY APPROVAL ONLY, IMPLEMENT YOUR PLAN BY CODING.
 

---

_**Assistant**_



---

I'll help you create TypeScript scripts for detecting missing translations and merging JSON files. Let me first explore your codebase to understand the current translation setup and usage patterns.

---

<details>
            <summary>Listed directory src • **4** results</summary>
        
| Name |
|-------|
| 📁 `app` |
| 📄 `middleware.ts` |
| 📁 `lib` |
| 📁 `i18n` |

</details>

---

<details>
            <summary>Searched codebase "translation hook useTranslation t( function usage" • **17** results</summary>
        
| File | Lines |
|------|-------|
| `src/i18n/index.ts` | L1-20 |
| `src/app/components/Header/MobileMoreMenu.tsx` | L28-59 |
| `src/app/components/common/CategoriesList.tsx` | L25-64 |
| `src/app/components/Header/SessionManager.tsx` | L169-215 |
| `src/app/components/Header/LanguageSwitcher.tsx` | L9-58 |
| `src/app/components/ProductDetail/ProductInformation.tsx` | L16-56 |
| `src/app/components/CartDrawer/CheckoutModal.tsx` | L64-93 |
| `src/app/product/[id]/page.tsx` | L1-44 |
| `src/app/categories/[slug]/page.tsx` | L22-63 |
| `src/app/components/ProductDetail/ProductInformation.tsx` | L179-196 |
| `src/app/components/Layout/AdminAuthenticatedLayout.tsx` | L1-11 |
| `src/app/components/SearchBar.tsx` | L12-61 |
| `src/app/components/CartDrawer/DeliveryInfo.tsx` | L6-31 |
| `src/app/components/Header/SessionManager.tsx` | L51-87 |
| `src/app/categories/[slug]/page.tsx` | L94-121 |
| `src/app/page.tsx` | L1-38 |
| `src/app/admin/components/ImportExportPanel.tsx` | L14-30 |

</details>

---

Read file: src/i18n/index.ts

---

<details>
            <summary>Grep search for "t\(" • **0** files</summary>
        
No results found
</details>

---

Model provided invalid arguments to grep tool.

---

<details>
            <summary>Grep search for "t\\(.*\\)" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Searched codebase "t('common.close' t('header.login' hardcoded strings" • **14** results</summary>
        
| File | Lines |
|------|-------|
| `src/i18n/locales/ta.json` | L1-23 |
| `src/i18n/locales/en.json` | L1-52 |
| `src/app/components/LoginModal.tsx` | L200-231 |
| `src/app/components/Header/MobileMoreMenu.tsx` | L125-149 |
| `src/app/components/LoginModal.tsx` | L231-252 |
| `src/app/components/Header/Header.tsx` | L310-326 |
| `src/app/components/Header/MobileMoreMenu.tsx` | L59-78 |
| `src/app/components/Header/MobileAccountSheet.tsx` | L69-100 |
| `src/app/components/Header/MobileMoreMenu.tsx` | L12-28 |
| `src/app/components/Header/Header.tsx` | L326-340 |
| `src/app/components/Header/MobileMoreMenu.tsx` | L104-125 |
| `src/i18n/locales/en.json` | L212-243 |
| `src/app/components/Header/MobileAccountSheet.tsx` | L100-112 |
| `src/app/components/Header/MobileMoreMenu.tsx` | L28-59 |

</details>

---

<details>
            <summary>Searched codebase "package.json" • **2** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |

</details>

---

Read file: package.json

---

Now let me look at some specific files to understand how hardcoded strings are used:

---

<details>
            <summary>Searched codebase "hardcoded strings "text" console.log className button title placeholder" • **21** results</summary>
        
| File | Lines |
|------|-------|
| `src/i18n/locales/en.json` | L98-144 |
| `src/i18n/locales/ta.json` | L101-121 |
| `src/i18n/locales/en.json` | L658-664 |
| `src/i18n/locales/ta.json` | L442-450 |
| `src/i18n/locales/en.json` | L144-178 |
| `src/i18n/locales/ta.json` | L156-178 |
| `src/i18n/locales/en.json` | L355-390 |
| `src/app/components/common/WhatsAppFloatingButton.tsx` | L55-77 |
| `src/app/components/LoginModal.tsx` | L231-252 |
| `src/i18n/locales/en.json` | L1-52 |
| `src/i18n/locales/ta.json` | L355-380 |
| `src/app/components/CartDrawer/CheckoutModal.tsx` | L210-229 |
| `src/i18n/locales/en.json` | L178-212 |
| `src/i18n/locales/ta.json` | L380-411 |
| `src/i18n/locales/en.json` | L52-98 |
| `src/i18n/locales/ta.json` | L627-636 |
| `src/i18n/locales/en.json` | L390-427 |
| `src/i18n/locales/en.json` | L427-461 |
| `src/app/components/LoginModal.tsx` | L278-295 |
| `src/app/components/Header/SessionManager.tsx` | L87-117 |
| `src/i18n/locales/ta.json` | L411-427 |

</details>

---

## What I Understood

Based on my analysis of your codebase, I can see the following:

**Current Translation Setup:**
1. You're using **react-i18next** for internationalization
2. Translation files are located in `src/i18n/locales/` (en.json, ta.json)
3. The translation function `t()` is used via `useTranslation()` hook
4. Translation keys follow a nested structure like `"loginModal.title"`, `"common.close"`, etc.
5. Some translations have fallback values: `t('key', 'Fallback Value')`

**Pattern Analysis:**
- Translation usage: `t('key')` or `t('key', 'fallback')`
- Hardcoded strings appear in: JSX text content, button labels, placeholders, aria-labels, console.log statements, error messages, etc.
- Current en.json has a well-structured nested format with sections like: header, common, session, product, admin, etc.

## My Plan

I will create **2 TypeScript scripts**:

### Script 1: `find-missing-translations.ts`
**Purpose:** Scan all .ts/.tsx files and identify hardcoded strings that should be translated

**Approach:**
1. **File Discovery:** Recursively find all .ts/.tsx files in src/ directory
2. **AST Parsing:** Use TypeScript compiler API to parse each file into AST
3. **String Extraction:** 
   - Extract string literals from JSX text content
   - Extract string literals from common props (placeholder, title, aria-label, etc.)
   - Skip strings that are already using `t()` function
   - Skip technical strings (URLs, class names, IDs, etc.)
   - Apply heuristics to filter out non-translatable strings
4. **Context Analysis:** Determine appropriate nested keys based on file location and component names
5. **JSON Generation:** Output missing.en.json with same nested structure as existing en.json

### Script 2: `merge-translations.ts`
**Purpose:** Merge en.json and missing.en.json with en.json taking priority

**Approach:**
1. **Deep Merge:** Recursively merge nested objects
2. **Conflict Resolution:** en.json values override missing.en.json values
3. **Structure Preservation:** Maintain the existing nested structure
4. **Output:** Generate merged.en.json file

**Key Features:**
- Intelligent string filtering (avoid CSS classes, IDs, URLs, etc.)
- Context-aware key generation based on file paths
- Support for nested object structures
- Conflict resolution with proper priority handling
- TypeScript with proper error handling

**Example Output Structure:**
```json
{
  "admin": {
    "newSection": {
      "someKey": "Some hardcoded string found",
      "anotherKey": "Another string"
    }
  },
  "components": {
    "headerSection": {
      "missingLabel": "Hardcoded Label"
    }
  }
}
```

Would you like me to proceed with implementing these scripts according to this plan?

---

