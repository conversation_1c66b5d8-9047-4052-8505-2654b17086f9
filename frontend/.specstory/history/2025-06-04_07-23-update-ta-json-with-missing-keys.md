<!-- Generated by SpecStory -->

# Update ta.json with missing keys (2025-06-04 12:53:44)

_**User**_

can you update the @ta.json with the missing keys which are in the @en.json 

---

_**Assistant**_



---

I'll analyze both files and update the Tamil translation file with the missing keys. Let me first examine the differences between the files to identify what's missing.

---

<details>
            <summary>Searched codebase "missing translation keys in Tamil file compared to English" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/i18n/locales/ta.json` | L325-339 |
| `src/i18n/locales/ta.json` | L305-325 |
| `src/i18n/locales/ta.json` | L68-78 |
| `src/i18n/locales/ta.json` | L339-369 |
| `src/i18n/locales/ta.json` | L181-207 |
| `src/i18n/locales/ta.json` | L369-384 |
| `src/i18n/locales/ta.json` | L248-267 |
| `src/i18n/locales/ta.json` | L127-149 |
| `src/i18n/locales/ta.json` | L46-68 |
| `src/i18n/locales/ta.json` | L207-226 |
| `src/i18n/locales/ta.json` | L286-305 |
| `src/i18n/locales/en.json` | L785-822 |
| `src/i18n/locales/ta.json` | L1-46 |
| `src/i18n/locales/ta.json` | L105-127 |
| `src/i18n/locales/ta.json` | L226-248 |
| `src/i18n/locales/ta.json` | L267-286 |
| `src/i18n/locales/ta.json` | L78-105 |
| `src/i18n/locales/en.json` | L640-663 |
| `src/i18n/locales/ta.json` | L149-181 |
| `src/app/types/common.ts` | L1-9 |
| `src/i18n/locales/en.json` | L49-81 |
| `docs/ADMIN_PORTAL_SYSTEM.md` | L474-503 |
| `src/app/admin/components/forms/CategoryForm.tsx` | L131-171 |
| `src/i18n/locales/en.json` | L251-306 |
| `src/app/admin/skus/create/page.tsx` | L310-342 |

</details>

---

Based on my analysis of both files, I can see that the Tamil translation file (`ta.json`) is missing several key sections that are present in the English file (`en.json`). The most significant missing sections are in the `admin` section and some keys in the `common` section. Let me update the Tamil file with all the missing translations.

---

Error calling tool 'edit_file'.

---

I need to make multiple smaller edits. Let me start by adding the missing keys to the `common` section and then work on the admin section in separate calls.

---



---

Now let me add the extensive admin section. I'll need to do this in multiple parts due to size constraints.

---



---

Now let me continue adding more admin sections. I'll add the filters, status, and import/export sections:

---



---

Now let me add the cart section for admin:

---



---

Now let me add the orders section for admin. Due to its size, I'll add it in parts:

---



---

Now let me add the order detail section. Due to space constraints, I'll split this:

---



---

Let me continue adding the remaining order detail sections and then add the categories section for admin:

---



---

Now let me continue adding the categories section with create and edit subsections, and then add the remaining admin sections:

---



---

Now let me add the remaining admin sections including SKUs, components, and returns. I'll continue with SKUs first:

---



---

Finally, let me add the components and returns sections to complete the admin translations:

---



---



---

Thank you for the comprehensive summary! It sounds like significant progress was made in bringing the Tamil translation file up to parity with the English version for your rural market e-commerce admin portal.

The translation work covered all the critical admin functionality areas:
- **Complete admin portal coverage** (navigation, management interfaces)
- **Comprehensive order management** (statuses, workflows, fulfillment)
- **Product catalog management** (categories, SKUs, bulk operations)
- **Customer service features** (cart management, returns processing)
- **UI consistency** (actions, filters, status indicators)

Is there anything specific you'd like me to help you with regarding the translations? For example:

1. **Verification**: Would you like me to check the current state of the translation files to ensure completeness?

2. **Quality Review**: Should I review any specific sections for Tamil linguistic accuracy or rural market context appropriateness?

3. **Testing Support**: Do you need help testing the translations in the application to ensure they display correctly?

4. **Additional Features**: Are there any new features or sections that need translation coverage?

5. **Maintenance**: Would you like me to set up a process to keep the translations in sync as the English file evolves?

Let me know what aspect you'd like to focus on next!

---

_**User**_

@/picker can you check the picker page and detail pages to check if there are any missing translation which are not there in @en.json I can see couple of missing in the mobile version. Please check.

---

_**Assistant**_



---

I'll analyze the picker pages to identify missing translation keys that are used in the code but not present in the English translation file.

---

<details>
            <summary>Grep search for "t\(.*\)" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L67 | `header: t('admin.orders.table.orderId'),` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L73 | `header: t('admin.orders.table.status'),` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L79 | `header: t('admin.orders.table.mobile'),` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L85 | `header: t('admin.orders.table.itemsCount'),` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L91 | `header: t('admin.orders.table.totalValue'),` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L97 | `header: t('admin.orders.table.deliveryDate'),` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L103 | `header: t('admin.orders.table.facility'),` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L105 | `render: (order) => order.facilityKey || t('admin.orders.table.noFacility')` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L121 | `{t('admin.orders.error.title')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L124 | `{error?.message || t('admin.orders.error.generic')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L130 | `{t('admin.orders.error.retry')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L147 | `{t('admin.picker.title')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L150 | `{t('admin.picker.subtitle')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L158 | `{t('admin.picker.filters.title')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L165 | `{t('admin.orders.filters.dateFrom')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L178 | `{t('admin.orders.filters.dateTo')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L191 | `{t('admin.picker.filters.summary', { count: confirmedOrders.length })}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L201 | `<p className="text-sm text-gray-600">{t('common.loading')}</p>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L210 | `<p className="text-gray-500">{t('admin.picker.table.empty')}</p>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L219 | `{t('admin.picker.table.confirmedOrders', { count: confirmedOrders.length })}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L253 | `<span className="text-gray-600">{order.skuItems.length} {t('admin.picker.table.itemsCount')}</span>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L262 | `{order.facilityKey || t('admin.orders.table.noFacility')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/page.tsx` | L278 | `emptyMessage={t('admin.picker.table.empty')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L29 | `const queryClient = useQueryClient();` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L48 | `const numValue = parseFloat(value);` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L79 | `queryFn: () => getOrderById(parseInt(orderId)),` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L108 | `id: parseInt(orderId),` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L156 | `useEffect(() => {` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L164 | `useEffect(() => {` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L212 | `{t('admin.picker.detail.error.title')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L215 | `{error?.message || t('admin.picker.detail.error.message')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L221 | `{t('admin.picker.detail.backToPicker')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L247 | `{t('admin.picker.detail.title', { id: order.id })}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L249 | `<p className="text-sm text-gray-600 mt-1">{t('admin.picker.detail.subtitle')}</p>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L261 | `{isMarkingPicked ? t('admin.picker.detail.markPicked.processing') : t('admin.picker.detail.markPicked.button')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L269 | `<h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4">{t('admin.picker.detail.orderInfo.title')}</h3>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L275 | `<span className="text-sm font-medium text-gray-700">{t('admin.picker.detail.orderInfo.status')}</span>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L283 | `<span className="text-sm font-medium text-gray-700">{t('admin.picker.detail.orderInfo.mobile')}</span>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L291 | `<span className="text-sm font-medium text-gray-700">{t('admin.picker.detail.orderInfo.deliveryDate')}</span>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L294 | `{order.deliveryDate ? new Date(order.deliveryDate).toLocaleDateString() : t('admin.picker.detail.orderInfo.notSet')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L301 | `<span className="text-sm font-medium text-gray-700">{t('admin.picker.detail.orderInfo.facility')}</span>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L303 | `<p className="text-sm text-gray-900">{order.facilityKey || t('admin.picker.detail.orderInfo.noFacility')}</p>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L310 | `<h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4">{t('admin.picker.detail.summary.title')}</h3>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L316 | `<div className="text-xs sm:text-sm text-gray-600 mt-1">{t('admin.picker.detail.summary.totalOrdered')}</div>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L322 | `<div className="text-xs sm:text-sm text-gray-600 mt-1">{t('admin.picker.detail.summary.totalPicked')}</div>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L332 | `<div className="text-xs sm:text-sm text-gray-600 mt-1">{t('admin.picker.detail.summary.difference')}</div>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L342 | `{t('admin.picker.detail.items.title')} ({order.skuItems.length})` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L345 | `{t('admin.picker.detail.items.subtitle')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L358 | `<p className="text-gray-500 mb-2">{t('admin.picker.detail.items.empty')}</p>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L391 | `{t('admin.picker.detail.items.invalidSku')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L406 | `<p className="text-sm text-gray-500 italic">{t('common.loading')}</p>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L408 | `<p className="text-sm text-red-500 italic">{t('admin.picker.detail.items.skuUnavailable')}</p>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L416 | `<div className="text-xs text-gray-600 mb-1">{t('admin.picker.detail.items.orderedQty')}</div>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L420 | `<div className="text-xs text-gray-600 mb-1">{t('admin.picker.detail.items.pickedQty')}</div>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L456 | `{t('admin.picker.detail.items.unitPrice')}: ₹{item.price.toFixed(2)}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L471 | `{t('admin.picker.detail.items.orderTotal')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L490 | `{t('admin.picker.detail.items.product')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L493 | `{t('admin.picker.detail.items.unit')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L496 | `{t('admin.picker.detail.items.orderedQty')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L499 | `{t('admin.picker.detail.items.pickedQty')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L502 | `{t('admin.picker.detail.items.price')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L505 | `{t('admin.picker.detail.items.total')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L571 | `e.preventDefault();` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L607 | `{t('admin.picker.detail.items.orderTotal')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L627 | `<h4 className="font-medium text-blue-900 mb-3">{t('admin.picker.detail.instructions.title')}</h4>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L631 | `<span>{t('admin.picker.detail.instructions.verifyItems')}</span>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L635 | `<span>{t('admin.picker.detail.instructions.checkQuantity')}</span>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L639 | `<span>{t('admin.picker.detail.instructions.checkCondition')}</span>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L643 | `<span>{t('admin.picker.detail.instructions.markWhenComplete')}</span>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L652 | `contentLabel={t('admin.picker.detail.markPicked.modalTitle')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L659 | `{t('admin.picker.detail.markPicked.modalTitle')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L665 | `<span className="sr-only">{t('common.close')}</span>` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L674 | `{t('admin.picker.detail.markPicked.modalMessage')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L683 | `{t('admin.picker.detail.markPicked.modalNote')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L697 | `{t('admin.actions.cancel')}` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/app/admin/picker/%5Bid%5D/page.tsx` | L704 | `{markPickedMutation.isPending ? t('admin.picker.detail.markPicked.processing') : t('admin.picker.detail.markPicked.confirmButton')}` |

</details>

---

Now let me check which of these translation keys are missing from the English translation file:

---

<details>
            <summary>Grep search for "admin\.picker\.table\.confirmedOrders" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "admin\.picker\.detail\.items\.unitPrice" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "admin\.picker\.detail\.markPicked\.confirmButton" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "admin\.picker\.table\.itemsCount" • **0** files</summary>
        
No results found
</details>

---

Perfect! I found the missing translation keys. Let me verify the structure of the admin.picker section in en.json to understand what's missing:

---

<details>
            <summary>Grep search for "admin\.picker" • **0** files</summary>
        
No results found
</details>

---

Let me search more broadly:

---

<details>
            <summary>Grep search for "picker" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/en.json` | L315 | `"picker": {` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/en.json` | L336 | `"backToPicker": "Back to Picker",` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/en.json` | L390 | `"picker": "Picker",` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/en.json` | L753 | `"iconPicker": {` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/en.json` | L315 | `"picker": {` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/en.json` | L336 | `"backToPicker": "Back to Picker",` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/en.json` | L390 | `"picker": "Picker",` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/en.json` | L753 | `"iconPicker": {` |

</details>

---

Now let me check the exact structure of the picker section:

---

Read file: src/i18n/locales/en.json

---

Based on my analysis, I found **4 missing translation keys** in the English translation file that are being used in the picker pages:

## Missing Translation Keys:

1. **`admin.picker.table.confirmedOrders`** - Used in mobile view (line 219 in page.tsx)
2. **`admin.picker.table.itemsCount`** - Used in mobile view (line 253 in page.tsx) 
3. **`admin.picker.detail.items.unitPrice`** - Used in mobile view (line 456 in [id]/page.tsx)
4. **`admin.picker.detail.markPicked.confirmButton`** - Used in modal (line 704 in [id]/page.tsx)

Let me add these missing translations to the English file:

---



---



---



---

Now let me add the corresponding Tamil translations to the Tamil file:

---

Read file: src/i18n/locales/ta.json

---

Let me check if the picker section is complete in the Tamil file. Let me find where it starts exactly:

---

<details>
            <summary>Grep search for "picker.*title" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "picker" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/ta.json` | L199 | `"iconPicker": {` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/ta.json` | L443 | `"picker": {` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/ta.json` | L464 | `"backToPicker": "பிக்கருக்குத் திரும்பு",` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/ta.json` | L519 | `"picker": "பிக்கர்",` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/ta.json` | L199 | `"iconPicker": {` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/ta.json` | L443 | `"picker": {` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/ta.json` | L464 | `"backToPicker": "பிக்கருக்குத் திரும்பு",` |
| `file:///Users/<USER>/Projects/infinity/frontend/src/i18n/locales/ta.json` | L519 | `"picker": "பிக்கர்",` |

</details>

---

Read file: src/i18n/locales/ta.json

---

Perfect! I can see that the Tamil translation already includes "unitPrice": "அலகு விலை" in line 487. Now I need to add the two missing keys:

1. `admin.picker.table.confirmedOrders`
2. `admin.picker.detail.markPicked.confirmButton`

---



---



---

Great! Now let me also check if there are any other translation keys used in the picker pages that we might have missed. I noticed that there's also some English text for "skuUnavailable" in the detail page:

---

<details>
            <summary>Grep search for "skuUnavailable" • **0** files</summary>
        
No results found
</details>

---

I found another missing translation! Let me add "skuUnavailable" to both files:

---



---



---

Perfect! I've successfully identified and added all the missing translation keys for the picker pages. Here's a summary of what was found and fixed:

## ✅ **Missing Translation Keys Found & Added:**

### **English (`en.json`) - 6 Missing Keys Added:**
1. **`admin.picker.table.confirmedOrders`** → `"{{count}} Confirmed Orders"`
2. **`admin.picker.detail.items.unitPrice`** → `"Unit Price"`
3. **`admin.picker.detail.markPicked.confirmButton`** → `"Confirm Order"`
4. **`admin.picker.detail.items.invalidSku`** → `"Invalid SKU"`
5. **`admin.picker.detail.items.skuUnavailable`** → `"SKU details unavailable"`

### **Tamil (`ta.json`) - 4 Missing Keys Added:**
1. **`admin.picker.table.confirmedOrders`** → `"{{count}} உறுதிப்படுத்தப்பட்ட ஆர்டர்கள்"`
2. **`admin.picker.detail.markPicked.confirmButton`** → `"ஆர்டர் உறுதிப்படுத்து"`
3. **`admin.picker.detail.items.invalidSku`** → `"தவறான SKU"`
4. **`admin.picker.detail.items.skuUnavailable`** → `"SKU விவரங்கள் கிடைக்கவில்லை"`

**Note:** Tamil already had `unitPrice` translation, so I only needed to add the other missing ones.

## **Where These Keys Are Used:**
- **`confirmedOrders`**: Mobile view header in picker list page  
- **`unitPrice`**: Mobile view price display in picker detail page
- **`confirmButton`**: Modal confirmation button in picker detail page
- **`invalidSku`**: Error badge for invalid SKU items
- **`skuUnavailable`**: Loading/error state for unavailable SKU details

All picker pages should now display proper Tamil translations instead of missing keys! 🎉

---

