/**
 * Storage adapters index file.
 *
 * Exports available storage adapters. Platform-specific adapters
 * (Node.js, React Native) are imported dynamically to avoid webpack issues.
 */

export { MemoryStorageAdapter } from './MemoryStorageAdapter';

// Platform-specific adapters are exported as dynamic imports
export const BrowserStorageAdapter = () => import('./BrowserStorageAdapter').then(m => m.BrowserStorageAdapter);
export const NodeStorageAdapter = () => import('./NodeStorageAdapter').then(m => m.NodeStorageAdapter);
export const ReactNativeStorageAdapter = () => import('./ReactNativeStorageAdapter').then(m => m.ReactNativeStorageAdapter);

// Re-export the base adapter for custom implementations
export { BaseStorageAdapter } from '../IStorageAdapter';

// Re-export types for convenience
export type {
    IStorageAdapter,
    StorageConfig,
    StorageCapabilities,
    StoredItemWithTimestamp,
    StorageResult,
    IStorageAdapterFactory,
    IStorageManager,
    PlatformInfo
} from '../types';
