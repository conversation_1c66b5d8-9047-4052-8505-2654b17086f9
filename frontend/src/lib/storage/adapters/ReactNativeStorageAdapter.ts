/**
 * React Native storage adapter using AsyncStorage.
 * 
 * This adapter provides storage functionality for React Native environments using
 * AsyncStorage for persistent data storage.
 */

import { BaseStorageAdapter } from '../IStorageAdapter';
import type { StorageConfig, StorageCapabilities, StoredItemWithTimestamp } from '../types';

// Dynamic import for AsyncStorage to avoid issues in non-React Native environments
let AsyncStorage: any = null;

// Check if we're in React Native environment and try to load AsyncStorage
if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {
    try {
        // Try to require AsyncStorage if available
        AsyncStorage = eval('require')('@react-native-async-storage/async-storage').default;
    } catch (error) {
        // AsyncStorage not available
    }
}

export class ReactNativeStorageAdapter extends BaseStorageAdapter {
    private keyPrefix: string = '';

    protected async initializeStorage(): Promise<void> {
        // AsyncStorage should already be loaded if we're in React Native
        if (!AsyncStorage) {
            // Try to load it again if not already loaded
            try {
                if (typeof require !== 'undefined') {
                    AsyncStorage = eval('require')('@react-native-async-storage/async-storage').default;
                }
            } catch (error) {
                throw new Error('AsyncStorage not available. Make sure @react-native-async-storage/async-storage is installed.');
            }
        }

        if (!AsyncStorage) {
            throw new Error('AsyncStorage not found. This adapter requires React Native environment.');
        }

        // Create a key prefix for this store instance
        this.keyPrefix = `${this.config.name || 'infinityAppDB'}:${this.config.storeName || 'default_store'}:`;

        // Test AsyncStorage to ensure it's working
        try {
            const testKey = `${this.keyPrefix}__test__`;
            await AsyncStorage.setItem(testKey, 'test');
            await AsyncStorage.removeItem(testKey);
        } catch (error) {
            throw new Error(`Failed to initialize React Native storage: ${error}`);
        }
    }

    protected async getStoredItem<T>(key: string): Promise<StoredItemWithTimestamp<T> | null> {
        if (!AsyncStorage) {
            throw new Error('AsyncStorage not initialized');
        }

        try {
            const fullKey = this.getFullKey(key);
            const jsonValue = await AsyncStorage.getItem(fullKey);
            
            if (jsonValue === null) {
                return null;
            }

            return JSON.parse(jsonValue);
        } catch (error) {
            throw new Error(`Failed to get item '${key}': ${error}`);
        }
    }

    protected async setStoredItem<T>(key: string, item: StoredItemWithTimestamp<T>): Promise<void> {
        if (!AsyncStorage) {
            throw new Error('AsyncStorage not initialized');
        }

        try {
            const fullKey = this.getFullKey(key);
            const jsonValue = JSON.stringify(item);
            await AsyncStorage.setItem(fullKey, jsonValue);
        } catch (error) {
            throw new Error(`Failed to set item '${key}': ${error}`);
        }
    }

    protected async removeStoredItem(key: string): Promise<void> {
        if (!AsyncStorage) {
            throw new Error('AsyncStorage not initialized');
        }

        try {
            const fullKey = this.getFullKey(key);
            await AsyncStorage.removeItem(fullKey);
        } catch (error) {
            throw new Error(`Failed to remove item '${key}': ${error}`);
        }
    }

    protected async clearStorage(): Promise<void> {
        if (!AsyncStorage) {
            throw new Error('AsyncStorage not initialized');
        }

        try {
            // Get all keys that belong to this store
            const allKeys = await AsyncStorage.getAllKeys();
            const storeKeys = allKeys.filter((key: string) => key.startsWith(this.keyPrefix));
            
            if (storeKeys.length > 0) {
                await AsyncStorage.multiRemove(storeKeys);
            }
        } catch (error) {
            throw new Error(`Failed to clear storage: ${error}`);
        }
    }

    protected async getStorageKeys(): Promise<string[]> {
        if (!AsyncStorage) {
            throw new Error('AsyncStorage not initialized');
        }

        try {
            const allKeys = await AsyncStorage.getAllKeys();
            const storeKeys = allKeys
                .filter((key: string) => key.startsWith(this.keyPrefix))
                .map((key: string) => key.substring(this.keyPrefix.length));
            
            return storeKeys;
        } catch (error) {
            throw new Error(`Failed to get storage keys: ${error}`);
        }
    }

    protected async getStorageLength(): Promise<number> {
        try {
            const keys = await this.getStorageKeys();
            return keys.length;
        } catch (error) {
            throw new Error(`Failed to get storage length: ${error}`);
        }
    }

    async getUsage(): Promise<{ used: number; available: number; total: number }> {
        // AsyncStorage doesn't provide direct usage information
        // We can estimate based on stored data
        try {
            const keys = await this.getStorageKeys();
            let totalSize = 0;

            // Estimate size by getting all items and measuring their JSON length
            for (const key of keys) {
                try {
                    const item = await this.getStoredItem(key);
                    if (item) {
                        totalSize += JSON.stringify(item).length * 2; // UTF-16 encoding
                    }
                } catch (error) {
                    // Skip items that can't be read
                }
            }

            // AsyncStorage typically has a 6MB limit on iOS, but this can vary
            const estimatedLimit = 6 * 1024 * 1024; // 6MB
            
            return {
                used: totalSize,
                available: Math.max(0, estimatedLimit - totalSize),
                total: estimatedLimit
            };
        } catch (error) {
            return { used: -1, available: -1, total: -1 };
        }
    }

    getCapabilities(): StorageCapabilities {
        return {
            supportsTTL: true, // We implement TTL ourselves
            supportsTransactions: false, // AsyncStorage doesn't support transactions
            supportsBulkOperations: true, // AsyncStorage has multiGet/multiSet
            maxStorageSize: 6 * 1024 * 1024, // Typical 6MB limit
            persistent: true,
            platform: 'react-native'
        };
    }

    async dispose(): Promise<void> {
        await super.dispose();
        AsyncStorage = null;
    }

    /**
     * Get the full key with prefix
     */
    private getFullKey(key: string): string {
        return `${this.keyPrefix}${this.getSafeKey(key)}`;
    }

    /**
     * Bulk get operation using AsyncStorage.multiGet
     */
    async getBulk<T>(keys: string[]): Promise<Array<{ key: string; value: T | null }>> {
        if (!AsyncStorage) {
            throw new Error('AsyncStorage not initialized');
        }

        try {
            const fullKeys = keys.map((key: string) => this.getFullKey(key));
            const results = await AsyncStorage.multiGet(fullKeys);
            
            return results.map(([fullKey, jsonValue]: [string, string | null], index: number) => {
                const originalKey = keys[index];
                let value: T | null = null;
                
                if (jsonValue !== null) {
                    try {
                        const item: StoredItemWithTimestamp<T> = JSON.parse(jsonValue);
                        
                        // Check if item is expired
                        if (!this.isItemExpired(item)) {
                            value = item.data;
                        } else {
                            // Remove expired item asynchronously
                            this.removeItem(originalKey).catch(() => {
                                // Ignore removal errors
                            });
                        }
                    } catch (parseError) {
                        // Invalid JSON, ignore
                    }
                }
                
                return { key: originalKey, value };
            });
        } catch (error) {
            throw new Error(`Failed to bulk get items: ${error}`);
        }
    }

    /**
     * Bulk set operation using AsyncStorage.multiSet
     */
    async setBulk<T>(items: Array<{ key: string; value: T; ttl?: number }>): Promise<void> {
        if (!AsyncStorage) {
            throw new Error('AsyncStorage not initialized');
        }

        try {
            const keyValuePairs = items.map(({ key, value, ttl }) => {
                const item: StoredItemWithTimestamp<T> = {
                    data: value,
                    timestamp: Date.now(),
                    ttl
                };
                
                return [this.getFullKey(key), JSON.stringify(item)];
            });
            
            await AsyncStorage.multiSet(keyValuePairs);
        } catch (error) {
            throw new Error(`Failed to bulk set items: ${error}`);
        }
    }

    /**
     * Bulk remove operation using AsyncStorage.multiRemove
     */
    async removeBulk(keys: string[]): Promise<void> {
        if (!AsyncStorage) {
            throw new Error('AsyncStorage not initialized');
        }

        try {
            const fullKeys = keys.map((key: string) => this.getFullKey(key));
            await AsyncStorage.multiRemove(fullKeys);
        } catch (error) {
            throw new Error(`Failed to bulk remove items: ${error}`);
        }
    }

    /**
     * Get all items in this store
     */
    async getAllItems<T>(): Promise<Array<{ key: string; value: T }>> {
        try {
            const keys = await this.getStorageKeys();
            const bulkResult = await this.getBulk<T>(keys);
            
            return bulkResult
                .filter(item => item.value !== null)
                .map(item => ({ key: item.key, value: item.value! }));
        } catch (error) {
            throw new Error(`Failed to get all items: ${error}`);
        }
    }

    /**
     * Clear all AsyncStorage data (not just this store)
     * Use with caution!
     */
    async clearAllAsyncStorage(): Promise<void> {
        if (!AsyncStorage) {
            throw new Error('AsyncStorage not initialized');
        }

        try {
            await AsyncStorage.clear();
        } catch (error) {
            throw new Error(`Failed to clear all AsyncStorage: ${error}`);
        }
    }

    /**
     * Get AsyncStorage info (if available)
     */
    async getAsyncStorageInfo(): Promise<any> {
        if (!AsyncStorage) {
            return null;
        }

        try {
            // Some versions of AsyncStorage provide additional info
            if (typeof AsyncStorage.getInfo === 'function') {
                return await AsyncStorage.getInfo();
            }
        } catch (error) {
            // Info not available
        }

        return null;
    }
}
