/**
 * Node.js storage adapter using file system.
 * 
 * This adapter provides storage functionality for Node.js environments using
 * the file system to persist data as JSON files.
 */

// Conditional imports for Node.js environment
let fs: any = null;
let path: any = null;

// Only import Node.js modules if we're in a Node.js environment
if (typeof process !== 'undefined' && process.versions && process.versions.node) {
    try {
        // Use dynamic import instead of require for better TypeScript compatibility
        const fsModule = eval('require')('fs');
        const pathModule = eval('require')('path');
        fs = fsModule.promises;
        path = pathModule;
    } catch (error) {
        // Node.js modules not available
    }
}
import { BaseStorageAdapter } from '../IStorageAdapter';
import type { StorageConfig, StorageCapabilities, StoredItemWithTimestamp } from '../types';

export class NodeStorageAdapter extends BaseStorageAdapter {
    private storageDir: string = '';
    private storeFile: string = '';

    protected async initializeStorage(): Promise<void> {
        // Determine storage directory
        const baseDir = this.config.platformOptions?.storageDir || 
                       process.env.STORAGE_DIR || 
                       path.join(process.cwd(), '.storage');

        this.storageDir = path.join(baseDir, this.config.name || 'infinityAppDB');
        this.storeFile = path.join(this.storageDir, `${this.config.storeName || 'default_store'}.json`);

        // Ensure storage directory exists
        try {
            await fs.mkdir(this.storageDir, { recursive: true });
        } catch (error) {
            throw new Error(`Failed to create storage directory: ${error}`);
        }

        // Initialize store file if it doesn't exist
        try {
            await fs.access(this.storeFile);
        } catch (error) {
            // File doesn't exist, create it with empty object
            await this.writeStoreFile({});
        }
    }

    protected async getStoredItem<T>(key: string): Promise<StoredItemWithTimestamp<T> | null> {
        try {
            const store = await this.readStoreFile();
            const item = store[this.getSafeKey(key)];
            return item || null;
        } catch (error) {
            throw new Error(`Failed to get item '${key}': ${error}`);
        }
    }

    protected async setStoredItem<T>(key: string, item: StoredItemWithTimestamp<T>): Promise<void> {
        try {
            const store = await this.readStoreFile();
            store[this.getSafeKey(key)] = item;
            await this.writeStoreFile(store);
        } catch (error) {
            throw new Error(`Failed to set item '${key}': ${error}`);
        }
    }

    protected async removeStoredItem(key: string): Promise<void> {
        try {
            const store = await this.readStoreFile();
            delete store[this.getSafeKey(key)];
            await this.writeStoreFile(store);
        } catch (error) {
            throw new Error(`Failed to remove item '${key}': ${error}`);
        }
    }

    protected async clearStorage(): Promise<void> {
        try {
            await this.writeStoreFile({});
        } catch (error) {
            throw new Error(`Failed to clear storage: ${error}`);
        }
    }

    protected async getStorageKeys(): Promise<string[]> {
        try {
            const store = await this.readStoreFile();
            return Object.keys(store);
        } catch (error) {
            throw new Error(`Failed to get storage keys: ${error}`);
        }
    }

    protected async getStorageLength(): Promise<number> {
        try {
            const store = await this.readStoreFile();
            return Object.keys(store).length;
        } catch (error) {
            throw new Error(`Failed to get storage length: ${error}`);
        }
    }

    async getUsage(): Promise<{ used: number; available: number; total: number }> {
        try {
            const stats = await fs.stat(this.storeFile);
            const used = stats.size;

            // Try to get disk space information
            let available = -1;
            let total = -1;

            try {
                // This is a simplified approach - in production you might want to use
                // a library like 'statvfs' for more accurate disk space information
                const diskStats = await fs.statfs(this.storageDir);
                available = diskStats.bavail * diskStats.bsize;
                total = diskStats.blocks * diskStats.bsize;
            } catch (error) {
                // Disk space information not available
            }

            return { used, available, total };
        } catch (error) {
            return { used: -1, available: -1, total: -1 };
        }
    }

    getCapabilities(): StorageCapabilities {
        return {
            supportsTTL: true, // We implement TTL ourselves
            supportsTransactions: false, // File system doesn't support transactions
            supportsBulkOperations: false, // We don't implement bulk operations
            maxStorageSize: -1, // Limited by disk space
            persistent: true,
            platform: 'node'
        };
    }

    async dispose(): Promise<void> {
        await super.dispose();
        // No specific cleanup needed for file system
    }

    /**
     * Read the store file and parse JSON
     */
    private async readStoreFile(): Promise<Record<string, any>> {
        try {
            const data = await fs.readFile(this.storeFile, 'utf-8');
            return JSON.parse(data);
        } catch (error) {
            if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
                // File doesn't exist, return empty object
                return {};
            }
            throw error;
        }
    }

    /**
     * Write data to the store file as JSON
     */
    private async writeStoreFile(data: Record<string, any>): Promise<void> {
        const jsonData = JSON.stringify(data, null, 2);
        
        // Write to temporary file first, then rename for atomic operation
        const tempFile = `${this.storeFile}.tmp`;
        
        try {
            await fs.writeFile(tempFile, jsonData, 'utf-8');
            await fs.rename(tempFile, this.storeFile);
        } catch (error) {
            // Clean up temp file if it exists
            try {
                await fs.unlink(tempFile);
            } catch (cleanupError) {
                // Ignore cleanup errors
            }
            throw error;
        }
    }

    /**
     * Get the storage directory path
     */
    getStorageDir(): string {
        return this.storageDir;
    }

    /**
     * Get the store file path
     */
    getStoreFile(): string {
        return this.storeFile;
    }

    /**
     * Backup the store to a specified location
     */
    async backup(backupPath: string): Promise<void> {
        try {
            await fs.copyFile(this.storeFile, backupPath);
        } catch (error) {
            throw new Error(`Failed to backup store: ${error}`);
        }
    }

    /**
     * Restore the store from a backup
     */
    async restore(backupPath: string): Promise<void> {
        try {
            await fs.copyFile(backupPath, this.storeFile);
        } catch (error) {
            throw new Error(`Failed to restore store: ${error}`);
        }
    }

    /**
     * Get file system statistics for the store
     */
    async getFileStats(): Promise<{
        size: number;
        created: Date;
        modified: Date;
        accessed: Date;
    }> {
        try {
            const stats = await fs.stat(this.storeFile);
            return {
                size: stats.size,
                created: stats.birthtime,
                modified: stats.mtime,
                accessed: stats.atime
            };
        } catch (error) {
            throw new Error(`Failed to get file stats: ${error}`);
        }
    }

    /**
     * Compact the store file by removing expired items
     */
    async compact(): Promise<number> {
        try {
            const store = await this.readStoreFile();
            const keys = Object.keys(store);
            let removedCount = 0;

            for (const key of keys) {
                const item = store[key];
                if (item && this.isItemExpired(item)) {
                    delete store[key];
                    removedCount++;
                }
            }

            if (removedCount > 0) {
                await this.writeStoreFile(store);
            }

            return removedCount;
        } catch (error) {
            throw new Error(`Failed to compact store: ${error}`);
        }
    }

    /**
     * Override getSafeKey to handle file system limitations
     */
    protected getSafeKey(key: string): string {
        // Replace characters that might be problematic in file systems
        return key.replace(/[<>:"/\\|?*]/g, '_');
    }
}
