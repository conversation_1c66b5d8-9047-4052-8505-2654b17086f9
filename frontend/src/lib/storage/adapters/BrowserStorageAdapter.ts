/**
 * Browser storage adapter using localforage.
 * 
 * This adapter provides storage functionality for browser environments using
 * localforage, which automatically selects the best available storage method
 * (IndexedDB → WebSQL → localStorage).
 */

import localforage from 'localforage';
import { BaseStorageAdapter } from '../IStorageAdapter';
import type { StorageConfig, StorageCapabilities, StoredItemWithTimestamp } from '../types';
import { isIndexedDBAvailable, isLocalStorageAvailable, getStorageLimitations } from '../platformDetection';

export class BrowserStorageAdapter extends BaseStorageAdapter {
    private storeInstance: LocalForage | null = null;

    protected async initializeStorage(): Promise<void> {
        // Configure localforage with the provided config
        const localforageConfig: LocalForageOptions = {
            name: this.config.name || 'infinityAppDB',
            storeName: this.config.storeName || 'default_store',
            version: this.config.version || 1.0,
            description: this.config.description || 'Storage for Infinity App'
        };

        // Set driver preference based on available technologies
        const drivers: string[] = [];
        if (isIndexedDBAvailable()) {
            drivers.push(localforage.INDEXEDDB);
        }
        if (isLocalStorageAvailable()) {
            drivers.push(localforage.LOCALSTORAGE);
        }
        // Note: WebSQL is deprecated, but localforage might still use it as fallback

        if (drivers.length > 0) {
            localforageConfig.driver = drivers;
        }

        // Apply any platform-specific options
        if (this.config.platformOptions) {
            Object.assign(localforageConfig, this.config.platformOptions);
        }

        // Create the localforage instance
        this.storeInstance = localforage.createInstance(localforageConfig);

        // Test the storage to ensure it's working
        try {
            await this.storeInstance.ready();
        } catch (error) {
            throw new Error(`Failed to initialize browser storage: ${error}`);
        }
    }

    protected async getStoredItem<T>(key: string): Promise<StoredItemWithTimestamp<T> | null> {
        if (!this.storeInstance) {
            throw new Error('Storage not initialized');
        }

        try {
            const item = await this.storeInstance.getItem<StoredItemWithTimestamp<T>>(this.getSafeKey(key));
            return item;
        } catch (error) {
            throw new Error(`Failed to get item '${key}': ${error}`);
        }
    }

    protected async setStoredItem<T>(key: string, item: StoredItemWithTimestamp<T>): Promise<void> {
        if (!this.storeInstance) {
            throw new Error('Storage not initialized');
        }

        try {
            await this.storeInstance.setItem(this.getSafeKey(key), item);
        } catch (error) {
            throw new Error(`Failed to set item '${key}': ${error}`);
        }
    }

    protected async removeStoredItem(key: string): Promise<void> {
        if (!this.storeInstance) {
            throw new Error('Storage not initialized');
        }

        try {
            await this.storeInstance.removeItem(this.getSafeKey(key));
        } catch (error) {
            throw new Error(`Failed to remove item '${key}': ${error}`);
        }
    }

    protected async clearStorage(): Promise<void> {
        if (!this.storeInstance) {
            throw new Error('Storage not initialized');
        }

        try {
            await this.storeInstance.clear();
        } catch (error) {
            throw new Error(`Failed to clear storage: ${error}`);
        }
    }

    protected async getStorageKeys(): Promise<string[]> {
        if (!this.storeInstance) {
            throw new Error('Storage not initialized');
        }

        try {
            return await this.storeInstance.keys();
        } catch (error) {
            throw new Error(`Failed to get storage keys: ${error}`);
        }
    }

    protected async getStorageLength(): Promise<number> {
        if (!this.storeInstance) {
            throw new Error('Storage not initialized');
        }

        try {
            return await this.storeInstance.length();
        } catch (error) {
            throw new Error(`Failed to get storage length: ${error}`);
        }
    }

    async getUsage(): Promise<{ used: number; available: number; total: number }> {
        // Try to get storage quota information if available
        if (typeof navigator !== 'undefined' && 'storage' in navigator && 'estimate' in navigator.storage) {
            try {
                const estimate = await navigator.storage.estimate();
                const used = estimate.usage || 0;
                const total = estimate.quota || 0;
                const available = total - used;

                return { used, available, total };
            } catch (error) {
                // Fall back to default implementation
            }
        }

        // Fallback to limitations-based estimation
        const limitations = getStorageLimitations();
        return {
            used: -1,
            available: limitations.maxSize > 0 ? limitations.maxSize : -1,
            total: limitations.maxSize
        };
    }

    getCapabilities(): StorageCapabilities {
        const limitations = getStorageLimitations();
        
        return {
            supportsTTL: true, // We implement TTL ourselves
            supportsTransactions: limitations.supportsTransactions,
            supportsBulkOperations: false, // localforage doesn't have native bulk operations
            maxStorageSize: limitations.maxSize,
            persistent: true,
            platform: 'browser'
        };
    }

    async dispose(): Promise<void> {
        await super.dispose();
        this.storeInstance = null;
    }

    /**
     * Get the current driver being used by localforage
     */
    getCurrentDriver(): string | null {
        if (!this.storeInstance) {
            return null;
        }

        return this.storeInstance.driver();
    }

    /**
     * Get localforage configuration
     */
    getLocalforageConfig(): LocalForageOptions | null {
        if (!this.storeInstance) {
            return null;
        }

        return this.storeInstance.config();
    }

    /**
     * Force localforage to use a specific driver
     */
    async setDriver(driver: string | string[]): Promise<void> {
        if (!this.storeInstance) {
            throw new Error('Storage not initialized');
        }

        try {
            await this.storeInstance.setDriver(driver);
        } catch (error) {
            throw new Error(`Failed to set driver: ${error}`);
        }
    }

    /**
     * Check if a specific driver is supported
     */
    isDriverSupported(driver: string): boolean {
        return localforage.supports(driver);
    }

    /**
     * Get all supported drivers
     */
    getSupportedDrivers(): string[] {
        const drivers: string[] = [];
        
        if (this.isDriverSupported(localforage.INDEXEDDB)) {
            drivers.push(localforage.INDEXEDDB);
        }
        
        if (this.isDriverSupported(localforage.WEBSQL)) {
            drivers.push(localforage.WEBSQL);
        }
        
        if (this.isDriverSupported(localforage.LOCALSTORAGE)) {
            drivers.push(localforage.LOCALSTORAGE);
        }

        return drivers;
    }
}
