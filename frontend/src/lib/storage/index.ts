/**
 * Storage abstraction layer main export file.
 * 
 * This module provides a platform-independent storage abstraction that works
 * across browser, Node.js, React Native, and other environments.
 */

// Core interfaces and types
export type {
    IStorageAdapter,
    IStorageAdapterFactory,
    IStorageManager,
    StorageConfig,
    StorageCapabilities,
    StoredItemWithTimestamp,
    StorageResult,
    PlatformInfo
} from './types';

// Base adapter class
export { BaseStorageAdapter } from './IStorageAdapter';

// Platform detection utilities
export {
    detectPlatform,
    isBrowser,
    isNode,
    isReactNative,
    isSSR,
    isLocalStorageAvailable,
    isIndexedDBAvailable,
    isWebSQLAvailable,
    isAsyncStorageAvailable,
    getAvailableStorageTechnologies,
    getRecommendedStorageAdapter,
    supportsPersistentStorage,
    getStorageLimitations,
    getReactNativeInfo
} from './platformDetection';

// Storage factory
export {
    StorageAdapterFactory,
    createStorageAdapter,
    createSpecificStorageAdapter,
    getStorageFactory
} from './StorageFactory';

// Storage manager
export {
    StorageManager,
    getStorageManager,
    getStorage
} from './StorageManager';

// Platform-specific adapters
export {
    BrowserStorageAdapter,
    NodeStorageAdapter,
    MemoryStorageAdapter,
    ReactNativeStorageAdapter
} from './adapters';

// Convenience functions for common use cases

/**
 * Initialize the storage system with global configuration
 */
export async function initializeStorage(config?: Partial<import('./types').StorageConfig>): Promise<void> {
    const { StorageManager } = await import('./StorageManager');
    const manager = StorageManager.getInstance();
    await manager.initialize(config || {
        name: 'infinityAppDB',
        version: 1,
        description: 'Storage for Infinity App'
    });
}

/**
 * Get a storage instance for a repository
 */
export async function getRepositoryStorage(
    repositoryName: string,
    ttl?: number
): Promise<import('./types').IStorageAdapter> {
    const { StorageManager } = await import('./StorageManager');
    const manager = StorageManager.getInstance();
    return await manager.getStorage(`${repositoryName}_store`, ttl);
}

/**
 * Create a storage adapter for testing
 */
export async function createTestStorage(storeName: string = 'test_store'): Promise<import('./types').IStorageAdapter> {
    const { StorageAdapterFactory } = await import('./StorageFactory');
    const factory = StorageAdapterFactory.getInstance();
    return await factory.createSpecificAdapter('memory', {
        name: 'test_db',
        storeName
    });
}

/**
 * Cleanup all storage instances
 */
export async function cleanupStorage(): Promise<void> {
    const { StorageManager } = await import('./StorageManager');
    const manager = StorageManager.getInstance();
    await manager.cleanup();
}

/**
 * Get storage health status
 */
export async function getStorageHealth(): Promise<{
    healthy: boolean;
    adapters: Array<{
        storeName: string;
        healthy: boolean;
        error?: string;
    }>;
}> {
    const { StorageManager } = await import('./StorageManager');
    const manager = StorageManager.getInstance();
    return await manager.getHealthStatus();
}
