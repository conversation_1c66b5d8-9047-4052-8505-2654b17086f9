/**
 * Core storage adapter interface and base implementation.
 * 
 * This module provides the main interface that all storage adapters must implement,
 * along with a base class that provides common functionality.
 */

import { logger } from '@/lib/logger';
import type {
    IStorageAdapter,
    StorageConfig,
    StorageCapabilities,
    StoredItemWithTimestamp
} from './types';

/**
 * Abstract base class for storage adapters.
 * 
 * Provides common functionality like TTL handling, error management,
 * and standardized logging. Platform-specific adapters should extend this class.
 */
export abstract class BaseStorageAdapter implements IStorageAdapter {
    protected config: StorageConfig;
    protected initialized = false;

    constructor() {
        this.config = {
            name: 'default',
            storeName: 'default'
        };
    }

    /**
     * Initialize the storage adapter with configuration
     */
    async initialize(config: StorageConfig): Promise<void> {
        this.config = { ...config };
        
        try {
            await this.initializeStorage();
            this.initialized = true;
            logger.debug(`Storage adapter initialized: ${this.config.storeName}`);
        } catch (error) {
            logger.error(`Failed to initialize storage adapter: ${this.config.storeName}`, error);
            throw new Error(`Storage initialization failed: ${error}`);
        }
    }

    /**
     * Platform-specific storage initialization
     * Must be implemented by concrete adapters
     */
    protected abstract initializeStorage(): Promise<void>;

    /**
     * Platform-specific item retrieval
     * Must be implemented by concrete adapters
     */
    protected abstract getStoredItem<T>(key: string): Promise<StoredItemWithTimestamp<T> | null>;

    /**
     * Platform-specific item storage
     * Must be implemented by concrete adapters
     */
    protected abstract setStoredItem<T>(key: string, item: StoredItemWithTimestamp<T>): Promise<void>;

    /**
     * Platform-specific item removal
     * Must be implemented by concrete adapters
     */
    protected abstract removeStoredItem(key: string): Promise<void>;

    /**
     * Platform-specific storage clearing
     * Must be implemented by concrete adapters
     */
    protected abstract clearStorage(): Promise<void>;

    /**
     * Platform-specific keys retrieval
     * Must be implemented by concrete adapters
     */
    protected abstract getStorageKeys(): Promise<string[]>;

    /**
     * Platform-specific length calculation
     * Must be implemented by concrete adapters
     */
    protected abstract getStorageLength(): Promise<number>;

    /**
     * Get an item from storage with TTL support
     */
    async getItem<T>(key: string): Promise<T | null> {
        this.ensureInitialized();

        try {
            const storedItem = await this.getStoredItem<T>(key);

            if (!storedItem) {
                return null;
            }

            // Check TTL if present
            if (this.isItemExpired(storedItem)) {
                logger.debug(`Item expired, removing: ${key}`);
                await this.removeItem(key);
                return null;
            }

            return storedItem.data;
        } catch (error) {
            logger.error(`Error getting item '${key}' from storage:`, error);
            return null;
        }
    }

    /**
     * Set an item in storage with optional TTL
     */
    async setItem<T>(key: string, value: T, ttl?: number): Promise<void> {
        this.ensureInitialized();

        try {
            const item: StoredItemWithTimestamp<T> = {
                data: value,
                timestamp: Date.now(),
                ttl
            };

            await this.setStoredItem(key, item);
        } catch (error) {
            logger.error(`Error setting item '${key}' in storage:`, error);
            throw error;
        }
    }

    /**
     * Remove an item from storage
     */
    async removeItem(key: string): Promise<void> {
        this.ensureInitialized();

        try {
            await this.removeStoredItem(key);
        } catch (error) {
            logger.error(`Error removing item '${key}' from storage:`, error);
            throw error;
        }
    }

    /**
     * Clear all items from storage
     */
    async clear(): Promise<void> {
        this.ensureInitialized();

        try {
            await this.clearStorage();
        } catch (error) {
            logger.error('Error clearing storage:', error);
            throw error;
        }
    }

    /**
     * Get all keys in storage
     */
    async keys(): Promise<string[]> {
        this.ensureInitialized();

        try {
            return await this.getStorageKeys();
        } catch (error) {
            logger.error('Error getting storage keys:', error);
            return [];
        }
    }

    /**
     * Get the number of items in storage
     */
    async length(): Promise<number> {
        this.ensureInitialized();

        try {
            return await this.getStorageLength();
        } catch (error) {
            logger.error('Error getting storage length:', error);
            return 0;
        }
    }

    /**
     * Check if storage is healthy
     */
    async isHealthy(): Promise<boolean> {
        if (!this.initialized) {
            return false;
        }

        try {
            // Test basic operations
            const testKey = '__health_check__';
            const testValue = { test: true, timestamp: Date.now() };
            
            await this.setItem(testKey, testValue);
            const retrieved = await this.getItem(testKey);
            await this.removeItem(testKey);

            return retrieved !== null &&
                   typeof retrieved === 'object' &&
                   (retrieved as any).test === true;
        } catch (error) {
            logger.error('Storage health check failed:', error);
            return false;
        }
    }

    /**
     * Get storage usage information
     */
    async getUsage(): Promise<{ used: number; available: number; total: number }> {
        // Default implementation - can be overridden by specific adapters
        return {
            used: -1,
            available: -1,
            total: -1
        };
    }

    /**
     * Cleanup expired items
     */
    async cleanup(): Promise<number> {
        this.ensureInitialized();

        try {
            const keys = await this.keys();
            let cleanedCount = 0;

            for (const key of keys) {
                try {
                    const storedItem = await this.getStoredItem(key);
                    if (storedItem && this.isItemExpired(storedItem)) {
                        await this.removeItem(key);
                        cleanedCount++;
                    }
                } catch (error) {
                    logger.warn(`Error checking expiry for key '${key}':`, error);
                }
            }

            if (cleanedCount > 0) {
                logger.info(`Cleaned up ${cleanedCount} expired items from storage`);
            }

            return cleanedCount;
        } catch (error) {
            logger.error('Error during storage cleanup:', error);
            return 0;
        }
    }

    /**
     * Dispose the storage adapter
     */
    async dispose(): Promise<void> {
        this.initialized = false;
        // Can be overridden by specific adapters for cleanup
    }

    /**
     * Get storage capabilities - must be implemented by concrete adapters
     */
    abstract getCapabilities(): StorageCapabilities;

    /**
     * Check if an item has expired based on TTL
     */
    protected isItemExpired<T>(item: StoredItemWithTimestamp<T>): boolean {
        if (!item.ttl || item.ttl <= 0) {
            return false;
        }

        const now = Date.now();
        return now > (item.timestamp + item.ttl);
    }

    /**
     * Ensure the adapter is initialized before operations
     */
    protected ensureInitialized(): void {
        if (!this.initialized) {
            throw new Error('Storage adapter not initialized. Call initialize() first.');
        }
    }

    /**
     * Get a safe storage key (can be overridden for platform-specific key formatting)
     */
    protected getSafeKey(key: string): string {
        return key;
    }
}
