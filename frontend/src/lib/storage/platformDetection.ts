/**
 * Platform detection utilities for determining the runtime environment.
 * 
 * This module provides functions to detect whether the code is running in
 * a browser, Node.js, React Native, or other environments, enabling the
 * selection of appropriate storage adapters.
 */

import type { PlatformInfo } from './types';

/**
 * Detect the current platform and environment
 */
export function detectPlatform(): PlatformInfo {
    const info: PlatformInfo = {
        platform: 'unknown',
        isServer: false,
        isBrowser: false,
        isReactNative: false,
        details: {}
    };

    // Check for React Native first (it has some browser-like globals)
    if (isReactNative()) {
        info.platform = 'react-native';
        info.isReactNative = true;
        info.details.reactNativeVersion = getReactNativeVersion();
        return info;
    }

    // Check for browser environment
    if (isBrowser()) {
        info.platform = 'browser';
        info.isBrowser = true;
        info.details.userAgent = getUserAgent();
        return info;
    }

    // Check for Node.js environment
    if (isNode()) {
        info.platform = 'node';
        info.isServer = true;
        info.details.nodeVersion = getNodeVersion();
        return info;
    }

    return info;
}

/**
 * Check if running in a browser environment
 */
export function isBrowser(): boolean {
    return typeof window !== 'undefined' && 
           typeof window.document !== 'undefined' &&
           typeof window.localStorage !== 'undefined';
}

/**
 * Check if running in Node.js environment
 */
export function isNode(): boolean {
    return typeof process !== 'undefined' && 
           process.versions != null && 
           process.versions.node != null &&
           typeof window === 'undefined';
}

/**
 * Check if running in React Native environment
 */
export function isReactNative(): boolean {
    return typeof navigator !== 'undefined' && 
           navigator.product === 'ReactNative';
}

/**
 * Check if running in a server-side rendering context
 */
export function isSSR(): boolean {
    return typeof window === 'undefined' && 
           typeof global !== 'undefined';
}

/**
 * Check if localStorage is available
 */
export function isLocalStorageAvailable(): boolean {
    try {
        if (typeof window === 'undefined' || !window.localStorage) {
            return false;
        }

        const testKey = '__localStorage_test__';
        window.localStorage.setItem(testKey, 'test');
        window.localStorage.removeItem(testKey);
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * Check if IndexedDB is available
 */
export function isIndexedDBAvailable(): boolean {
    try {
        return typeof window !== 'undefined' && 
               'indexedDB' in window && 
               window.indexedDB !== null;
    } catch (error) {
        return false;
    }
}

/**
 * Check if WebSQL is available (deprecated but still used as fallback)
 */
export function isWebSQLAvailable(): boolean {
    try {
        return typeof window !== 'undefined' && 
               'openDatabase' in window;
    } catch (error) {
        return false;
    }
}

/**
 * Check if AsyncStorage is available (React Native)
 */
export function isAsyncStorageAvailable(): boolean {
    try {
        // Try to import AsyncStorage dynamically
        return isReactNative() && 
               typeof require !== 'undefined';
    } catch (error) {
        return false;
    }
}

/**
 * Get user agent string (browser only)
 */
function getUserAgent(): string | undefined {
    try {
        return typeof navigator !== 'undefined' ? navigator.userAgent : undefined;
    } catch (error) {
        return undefined;
    }
}

/**
 * Get Node.js version (Node.js only)
 */
function getNodeVersion(): string | undefined {
    try {
        return typeof process !== 'undefined' ? process.version : undefined;
    } catch (error) {
        return undefined;
    }
}

/**
 * Get React Native version (React Native only)
 */
function getReactNativeVersion(): string | undefined {
    try {
        // This is a simplified check - in real apps you might want to
        // check the actual React Native version from package.json or other sources
        return isReactNative() ? 'unknown' : undefined;
    } catch (error) {
        return undefined;
    }
}

/**
 * Get available storage technologies for the current platform
 */
export function getAvailableStorageTechnologies(): string[] {
    const available: string[] = [];

    if (isLocalStorageAvailable()) {
        available.push('localStorage');
    }

    if (isIndexedDBAvailable()) {
        available.push('indexedDB');
    }

    if (isWebSQLAvailable()) {
        available.push('webSQL');
    }

    if (isAsyncStorageAvailable()) {
        available.push('asyncStorage');
    }

    if (isNode()) {
        available.push('fileSystem');
    }

    // Memory storage is always available
    available.push('memory');

    return available;
}

/**
 * Get the recommended storage adapter for the current platform
 */
export function getRecommendedStorageAdapter(): string {
    const platform = detectPlatform();

    switch (platform.platform) {
        case 'browser':
            if (isIndexedDBAvailable()) return 'browser'; // Uses localforage with IndexedDB
            if (isLocalStorageAvailable()) return 'browser'; // Uses localforage with localStorage
            return 'memory';

        case 'node':
            return 'node'; // Uses file system

        case 'react-native':
            return 'react-native'; // Uses AsyncStorage

        default:
            return 'memory'; // Fallback to memory storage
    }
}

/**
 * Check if the current environment supports persistent storage
 */
export function supportsPersistentStorage(): boolean {
    const platform = detectPlatform();
    
    switch (platform.platform) {
        case 'browser':
            return isLocalStorageAvailable() || isIndexedDBAvailable();
        case 'node':
            return true; // File system is persistent
        case 'react-native':
            return isAsyncStorageAvailable();
        default:
            return false;
    }
}

/**
 * Get platform-specific storage limitations
 */
export function getStorageLimitations(): {
    maxSize: number; // in bytes, -1 for unlimited
    supportsComplexTypes: boolean;
    supportsTransactions: boolean;
    supportsTTL: boolean;
} {
    const platform = detectPlatform();

    switch (platform.platform) {
        case 'browser':
            return {
                maxSize: isIndexedDBAvailable() ? 50 * 1024 * 1024 : 5 * 1024 * 1024, // 50MB for IndexedDB, 5MB for localStorage
                supportsComplexTypes: true,
                supportsTransactions: isIndexedDBAvailable(),
                supportsTTL: false // We implement TTL ourselves
            };

        case 'node':
            return {
                maxSize: -1, // Limited by disk space
                supportsComplexTypes: true,
                supportsTransactions: false, // We don't implement transactions for file system
                supportsTTL: false // We implement TTL ourselves
            };

        case 'react-native':
            return {
                maxSize: 6 * 1024 * 1024, // 6MB typical limit
                supportsComplexTypes: true,
                supportsTransactions: false,
                supportsTTL: false // We implement TTL ourselves
            };

        default:
            return {
                maxSize: -1,
                supportsComplexTypes: true,
                supportsTransactions: false,
                supportsTTL: false
            };
    }
}
