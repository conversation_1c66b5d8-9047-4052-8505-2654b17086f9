/**
 * Tests for storage abstraction layer platform independence.
 * 
 * These tests verify that the storage abstraction works correctly
 * across different platforms and adapters.
 */

// Using Jest instead of Vitest
import { MemoryStorageAdapter } from '../adapters/MemoryStorageAdapter';
import { StorageAdapterFactory } from '../StorageFactory';
import { StorageManager } from '../StorageManager';
import { detectPlatform, getRecommendedStorageAdapter } from '../platformDetection';

describe('Storage Abstraction Platform Independence', () => {
    let memoryAdapter: MemoryStorageAdapter;
    let storageManager: StorageManager;

    beforeEach(async () => {
        // Use memory adapter for testing
        memoryAdapter = new MemoryStorageAdapter();
        await memoryAdapter.initialize({
            name: 'test_db',
            storeName: 'test_store'
        });

        // Create a fresh storage manager instance
        storageManager = new StorageManager();
        await storageManager.initialize({
            name: 'test_db',
            version: 1
        });
    });

    afterEach(async () => {
        if (memoryAdapter) {
            await memoryAdapter.dispose();
        }
        if (storageManager) {
            await storageManager.cleanup();
        }
    });

    describe('Memory Storage Adapter', () => {
        it('should store and retrieve data correctly', async () => {
            const testData = { message: 'Hello, World!', timestamp: Date.now() };
            
            await memoryAdapter.setItem('test_key', testData);
            const retrieved = await memoryAdapter.getItem('test_key');
            
            expect(retrieved).toEqual(testData);
        });

        it('should handle TTL correctly', async () => {
            const testData = { message: 'Expires soon' };
            const shortTTL = 100; // 100ms
            
            await memoryAdapter.setItem('ttl_key', testData, shortTTL);
            
            // Should be available immediately
            let retrieved = await memoryAdapter.getItem('ttl_key');
            expect(retrieved).toEqual(testData);
            
            // Wait for expiration
            await new Promise(resolve => setTimeout(resolve, 150));
            
            // Should be expired now
            retrieved = await memoryAdapter.getItem('ttl_key');
            expect(retrieved).toBeNull();
        });

        it('should support all basic operations', async () => {
            // Set multiple items
            await memoryAdapter.setItem('key1', 'value1');
            await memoryAdapter.setItem('key2', 'value2');
            await memoryAdapter.setItem('key3', 'value3');
            
            // Check length
            const length = await memoryAdapter.length();
            expect(length).toBe(3);
            
            // Get keys
            const keys = await memoryAdapter.keys();
            expect(keys).toContain('key1');
            expect(keys).toContain('key2');
            expect(keys).toContain('key3');
            
            // Remove one item
            await memoryAdapter.removeItem('key2');
            const newLength = await memoryAdapter.length();
            expect(newLength).toBe(2);
            
            // Clear all
            await memoryAdapter.clear();
            const finalLength = await memoryAdapter.length();
            expect(finalLength).toBe(0);
        });

        it('should report correct capabilities', () => {
            const capabilities = memoryAdapter.getCapabilities();
            
            expect(capabilities.platform).toBe('memory');
            expect(capabilities.persistent).toBe(false);
            expect(capabilities.supportsTTL).toBe(true);
            expect(capabilities.supportsBulkOperations).toBe(true);
        });

        it('should handle health checks', async () => {
            const isHealthy = await memoryAdapter.isHealthy();
            expect(isHealthy).toBe(true);
        });
    });

    describe('Storage Factory', () => {
        it('should create appropriate adapter for current platform', async () => {
            const factory = StorageAdapterFactory.getInstance();
            const adapterType = factory.getAdapterType();
            
            // In test environment, should default to memory
            expect(['memory', 'browser', 'node'].includes(adapterType)).toBe(true);
        });

        it('should create memory adapter when requested', async () => {
            const factory = StorageAdapterFactory.getInstance();
            const adapter = await factory.createSpecificAdapter('memory', {
                name: 'test_db',
                storeName: 'test_store'
            });
            
            expect(adapter).toBeInstanceOf(MemoryStorageAdapter);
            expect(adapter.getCapabilities().platform).toBe('memory');
            
            await adapter.dispose();
        });

        it('should report available adapter types', () => {
            const factory = StorageAdapterFactory.getInstance();
            const availableTypes = factory.getAvailableAdapterTypes();
            
            expect(availableTypes).toContain('memory');
            // Other types depend on environment
        });
    });

    describe('Storage Manager', () => {
        it('should manage multiple storage instances', async () => {
            const storage1 = await storageManager.getStorage('store1');
            const storage2 = await storageManager.getStorage('store2');
            
            // Should be different instances
            expect(storage1).not.toBe(storage2);
            
            // Should work independently
            await storage1.setItem('key', 'value1');
            await storage2.setItem('key', 'value2');
            
            const value1 = await storage1.getItem('key');
            const value2 = await storage2.getItem('key');
            
            expect(value1).toBe('value1');
            expect(value2).toBe('value2');
        });

        it('should provide health status for all storages', async () => {
            await storageManager.getStorage('store1');
            await storageManager.getStorage('store2');
            
            const healthStatus = await storageManager.getHealthStatus();
            
            expect(healthStatus.healthy).toBe(true);
            expect(healthStatus.adapters).toHaveLength(2);
            expect(healthStatus.adapters[0].healthy).toBe(true);
            expect(healthStatus.adapters[1].healthy).toBe(true);
        });

        it('should cleanup all storages', async () => {
            const storage1 = await storageManager.getStorage('store1');
            const storage2 = await storageManager.getStorage('store2');
            
            // Add some data with TTL
            await storage1.setItem('key1', 'value1', 100);
            await storage2.setItem('key2', 'value2', 100);
            
            // Wait for expiration
            await new Promise(resolve => setTimeout(resolve, 150));
            
            // Cleanup should remove expired items
            const cleanupResults = await storageManager.performGlobalCleanup();
            
            expect(cleanupResults['store1']).toBeGreaterThanOrEqual(0);
            expect(cleanupResults['store2']).toBeGreaterThanOrEqual(0);
        });
    });

    describe('Platform Detection', () => {
        it('should detect platform information', () => {
            const platformInfo = detectPlatform();

            expect(platformInfo).toHaveProperty('platform');
            expect(platformInfo).toHaveProperty('isServer');
            expect(platformInfo).toHaveProperty('isBrowser');
            expect(platformInfo).toHaveProperty('isReactNative');
            expect(platformInfo).toHaveProperty('details');
        });

        it('should recommend appropriate storage adapter', () => {
            const recommendedAdapter = getRecommendedStorageAdapter();

            expect(typeof recommendedAdapter).toBe('string');
            expect(['browser', 'node', 'react-native', 'memory'].includes(recommendedAdapter)).toBe(true);
        });

        it('should handle React Native version detection gracefully', () => {
            // Import the function we want to test
            const { getReactNativeInfo } = require('../platformDetection');

            // In a non-React Native environment, this should return undefined
            const rnInfo = getReactNativeInfo();
            expect(rnInfo).toBeUndefined();
        });

        it('should provide meaningful React Native version fallbacks', () => {
            // Mock React Native environment
            const originalNavigator = global.navigator;
            const originalGlobal = global;

            // Mock navigator to simulate React Native
            (global as any).navigator = {
                userAgent: 'ReactNative/0.72.0',
                product: 'ReactNative'
            };

            // Mock global React Native environment
            (global as any).HermesInternal = {
                getRuntimeProperties: () => ({
                    'OSS Release Version': '0.12.0'
                })
            };

            try {
                const { getReactNativeInfo } = require('../platformDetection');
                const rnInfo = getReactNativeInfo();

                if (rnInfo) {
                    expect(rnInfo).toHaveProperty('version');
                    expect(rnInfo).toHaveProperty('engine');
                    expect(rnInfo).toHaveProperty('bundler');
                    expect(rnInfo).toHaveProperty('isDevelopment');
                    expect(rnInfo).toHaveProperty('capabilities');
                    expect(Array.isArray(rnInfo.capabilities)).toBe(true);
                }
            } finally {
                // Restore original globals
                global.navigator = originalNavigator;
                delete (global as any).HermesInternal;
            }
        });

        it('should detect React Native version from different sources', () => {
            // Test version detection with different mock scenarios
            const testCases = [
                {
                    name: 'User Agent Version',
                    setup: () => {
                        (global as any).navigator = {
                            userAgent: 'ReactNative/0.71.8',
                            product: 'ReactNative'
                        };
                    },
                    expectedVersionPattern: /useragent-0\.71\.8/
                },
                {
                    name: 'Hermes Engine Version',
                    setup: () => {
                        (global as any).navigator = { product: 'ReactNative' };
                        (global as any).HermesInternal = {
                            getRuntimeProperties: () => ({
                                'OSS Release Version': '0.12.0'
                            })
                        };
                    },
                    expectedVersionPattern: /hermes-0\.12\.0/
                },
                {
                    name: 'Environment Detection',
                    setup: () => {
                        (global as any).navigator = { product: 'ReactNative' };
                        (global as any).__DEV__ = true;
                        (global as any).__METRO__ = true;
                    },
                    expectedVersionPattern: /env-/
                }
            ];

            testCases.forEach(testCase => {
                // Clean up globals
                delete (global as any).navigator;
                delete (global as any).HermesInternal;
                delete (global as any).__DEV__;
                delete (global as any).__METRO__;

                // Setup test case
                testCase.setup();

                try {
                    // Re-require to get fresh module state
                    delete require.cache[require.resolve('../platformDetection')];
                    const { getReactNativeInfo } = require('../platformDetection');

                    const rnInfo = getReactNativeInfo();
                    if (rnInfo && rnInfo.version) {
                        expect(rnInfo.version).toMatch(testCase.expectedVersionPattern);
                    }
                } catch (error) {
                    // Some test cases might not work in all environments, which is fine
                    console.warn(`Test case "${testCase.name}" failed:`, error instanceof Error ? error.message : String(error));
                }
            });

            // Clean up after all tests
            delete (global as any).navigator;
            delete (global as any).HermesInternal;
            delete (global as any).__DEV__;
            delete (global as any).__METRO__;
        });
    });

    describe('Cross-Platform Compatibility', () => {
        it('should handle complex data types consistently', async () => {
            const complexData = {
                string: 'test',
                number: 42,
                boolean: true,
                array: [1, 2, 3],
                object: { nested: 'value' },
                date: new Date().toISOString(),
                null: null,
                undefined: undefined
            };
            
            await memoryAdapter.setItem('complex', complexData);
            const retrieved = await memoryAdapter.getItem('complex');
            
            expect(retrieved).toEqual({
                ...complexData,
                undefined: undefined // undefined might be serialized differently
            });
        });

        it('should handle large data sets', async () => {
            const largeArray = Array.from({ length: 1000 }, (_, i) => ({
                id: i,
                data: `Item ${i}`,
                timestamp: Date.now() + i
            }));
            
            await memoryAdapter.setItem('large_data', largeArray);
            const retrieved = await memoryAdapter.getItem('large_data') as typeof largeArray;

            expect(retrieved).toHaveLength(1000);
            expect(retrieved[0]).toEqual(largeArray[0]);
            expect(retrieved[999]).toEqual(largeArray[999]);
        });

        it('should handle concurrent operations', async () => {
            const operations = Array.from({ length: 10 }, async (_, i) => {
                await memoryAdapter.setItem(`concurrent_${i}`, `value_${i}`);
                return memoryAdapter.getItem(`concurrent_${i}`);
            });
            
            const results = await Promise.all(operations);
            
            results.forEach((result, i) => {
                expect(result).toBe(`value_${i}`);
            });
        });
    });

    describe('Error Handling', () => {
        it('should handle storage errors gracefully', async () => {
            // Create a new adapter instance for this test to avoid affecting other tests
            const testAdapter = new MemoryStorageAdapter();
            await testAdapter.initialize({ name: 'error_test', storeName: 'error_store' });

            // Dispose the adapter to simulate storage failure
            await testAdapter.dispose();

            // Operations should throw meaningful errors when adapter is not initialized
            await expect(testAdapter.getItem('test')).rejects.toThrow('Storage adapter not initialized');

            const isHealthy = await testAdapter.isHealthy();
            expect(isHealthy).toBe(false);
        });

        it('should handle invalid keys gracefully', async () => {
            // Test with various invalid keys
            const invalidKeys = ['', null, undefined];
            
            for (const key of invalidKeys) {
                try {
                    await memoryAdapter.setItem(key as any, 'value');
                    const result = await memoryAdapter.getItem(key as any);
                    // Should either work or return null, but not throw
                    expect(result === null || result === 'value').toBe(true);
                } catch (error) {
                    // If it throws, it should be a meaningful error
                    expect(error).toBeInstanceOf(Error);
                }
            }
        });
    });
});
