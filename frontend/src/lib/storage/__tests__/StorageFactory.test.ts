/**
 * Tests for StorageFactory refactoring
 * 
 * This test file verifies that the refactored StorageFactory maintains
 * all existing functionality while eliminating code duplication.
 */

import { StorageAdapterFactory } from '../StorageFactory';
import { MemoryStorageAdapter } from '../adapters/MemoryStorageAdapter';

describe('StorageFactory Refactoring', () => {
    let factory: StorageAdapterFactory;

    beforeEach(() => {
        factory = StorageAdapterFactory.getInstance();
    });

    describe('createAdapter', () => {
        it('should create appropriate adapter for current platform', async () => {
            const config = { name: 'test_db', storeName: 'test_store' };
            const adapter = await factory.createAdapter(config);
            
            expect(adapter).toBeDefined();
            expect(typeof adapter.getItem).toBe('function');
            expect(typeof adapter.setItem).toBe('function');
            
            await adapter.dispose();
        });

        it('should fallback to memory adapter on failure', async () => {
            const config = { name: 'test_db', storeName: 'test_store' };
            
            // This should work and potentially fallback to memory if needed
            const adapter = await factory.createAdapter(config);
            expect(adapter).toBeDefined();
            
            await adapter.dispose();
        });
    });

    describe('createSpecificAdapter', () => {
        it('should create memory adapter when requested', async () => {
            const config = { name: 'test_db', storeName: 'test_store' };
            const adapter = await factory.createSpecificAdapter('memory', config);
            
            expect(adapter).toBeInstanceOf(MemoryStorageAdapter);
            expect(adapter.getCapabilities().platform).toBe('memory');
            
            await adapter.dispose();
        });

        it('should throw error for unavailable adapter types', async () => {
            const config = { name: 'test_db', storeName: 'test_store' };
            
            // Try to create an adapter that's not available in current environment
            // This will depend on the current platform, but we can test with a fake type
            await expect(
                factory.createSpecificAdapter('fake-adapter', config)
            ).rejects.toThrow('not available in this environment');
        });

        it('should throw error for unknown adapter types', async () => {
            const config = { name: 'test_db', storeName: 'test_store' };
            
            // Mock isAdapterAvailable to return true for unknown type
            const originalIsAvailable = factory.isAdapterAvailable;
            factory.isAdapterAvailable = jest.fn().mockReturnValue(true);
            
            try {
                await expect(
                    factory.createSpecificAdapter('unknown-type', config)
                ).rejects.toThrow('Unknown storage adapter type: unknown-type');
            } finally {
                factory.isAdapterAvailable = originalIsAvailable;
            }
        });
    });

    describe('createAdapterWithFallback', () => {
        it('should create adapter with fallback chain', async () => {
            const config = { name: 'test_db', storeName: 'test_store' };
            const adapter = await factory.createAdapterWithFallback(config, ['memory']);
            
            expect(adapter).toBeInstanceOf(MemoryStorageAdapter);
            
            await adapter.dispose();
        });

        it('should try multiple adapter types in order', async () => {
            const config = { name: 'test_db', storeName: 'test_store' };
            
            // Try with a fallback chain that includes memory as last resort
            const adapter = await factory.createAdapterWithFallback(config, ['fake-type', 'memory']);
            
            expect(adapter).toBeInstanceOf(MemoryStorageAdapter);
            
            await adapter.dispose();
        });

        it('should throw error when no adapters can be created', async () => {
            const config = { name: 'test_db', storeName: 'test_store' };
            
            // Mock isAdapterAvailable to always return false
            const originalIsAvailable = factory.isAdapterAvailable;
            factory.isAdapterAvailable = jest.fn().mockReturnValue(false);
            
            try {
                await expect(
                    factory.createAdapterWithFallback(config, ['fake-type'])
                ).rejects.toThrow('No storage adapter could be created');
            } finally {
                factory.isAdapterAvailable = originalIsAvailable;
            }
        });
    });

    describe('adapter availability checks', () => {
        it('should correctly identify available adapter types', () => {
            const availableTypes = factory.getAvailableAdapterTypes();
            
            expect(Array.isArray(availableTypes)).toBe(true);
            expect(availableTypes.includes('memory')).toBe(true); // Memory is always available
        });

        it('should check memory adapter availability', () => {
            expect(factory.isAdapterAvailable('memory')).toBe(true);
        });

        it('should return false for unknown adapter types', () => {
            expect(factory.isAdapterAvailable('unknown-type')).toBe(false);
        });
    });

    describe('singleton pattern', () => {
        it('should return same instance', () => {
            const factory1 = StorageAdapterFactory.getInstance();
            const factory2 = StorageAdapterFactory.getInstance();
            
            expect(factory1).toBe(factory2);
        });
    });

    describe('error handling consistency', () => {
        it('should handle adapter creation errors gracefully', async () => {
            const config = { name: 'test_db', storeName: 'test_store' };
            
            // Test that both methods handle errors consistently
            // This is more of an integration test to ensure refactoring didn't break error handling
            try {
                const adapter1 = await factory.createAdapter(config);
                const adapter2 = await factory.createSpecificAdapter('memory', config);
                
                expect(adapter1).toBeDefined();
                expect(adapter2).toBeDefined();
                
                await adapter1.dispose();
                await adapter2.dispose();
            } catch (error) {
                // If there's an error, it should be meaningful
                expect(error).toBeInstanceOf(Error);
                expect(typeof (error as Error).message).toBe('string');
            }
        });
    });
});
