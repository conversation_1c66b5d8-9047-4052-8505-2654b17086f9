# Platform-Independent Storage Abstraction

This module provides a comprehensive storage abstraction layer that enables the codebase to work across different platforms (browser, Node.js, React Native) without being tied to browser-specific APIs like `localStorage` or `localforage`.

## Overview

The storage abstraction eliminates platform dependencies by providing:

- **Platform Detection**: Automatically detects the runtime environment
- **Adapter Pattern**: Platform-specific storage implementations with a common interface
- **Dependency Injection**: Centralized storage management and configuration
- **TTL Support**: Built-in time-to-live functionality across all platforms
- **Migration Support**: Seamless migration from existing localStorage usage

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
│  (Repositories, Context files, Components)                  │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Storage Abstraction Interface                │
│  (IStorageAdapter with get, set, remove, clear methods)     │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              Platform-Specific Adapters                     │
│  ┌─────────────────┐ ┌─────────────────┐ ┌───────────────┐  │
│  │ BrowserStorage  │ │  NodeStorage    │ │ MemoryStorage │  │
│  │   (localforage) │ │ (file system)   │ │  (testing)    │  │
│  └─────────────────┘ └─────────────────┘ └───────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## Platform Support

| Platform | Storage Backend | Persistent | TTL | Max Size |
|----------|----------------|------------|-----|----------|
| **Browser** | localforage (IndexedDB → localStorage) | ✅ | ✅ | ~50MB |
| **Node.js** | File system (JSON files) | ✅ | ✅ | Unlimited |
| **React Native** | AsyncStorage | ✅ | ✅ | ~6MB |
| **Memory** | In-memory Map | ❌ | ✅ | Configurable |

## Quick Start

### Basic Usage

```typescript
import { initializeStorage, getRepositoryStorage } from '@/lib/storage';

// Initialize the storage system (call once at app startup)
await initializeStorage({
    name: 'myAppDB',
    version: 1,
    description: 'My Application Storage'
});

// Get a storage instance for a specific repository
const storage = await getRepositoryStorage('user_data');

// Use the storage
await storage.setItem('user_id', '12345');
const userId = await storage.getItem('user_id');
await storage.removeItem('user_id');
```

### Repository Integration

```typescript
import { RepositoryBase } from '@/app/repository/RepositoryBase';

class MyRepository extends RepositoryBase {
    constructor() {
        super('my_store', 3600000); // 1 hour TTL
    }

    async saveUserData(data: UserData): Promise<void> {
        await this.setItem('user_data', data);
    }

    async getUserData(): Promise<UserData | null> {
        return await this.getItem<UserData>('user_data');
    }
}
```

## API Reference

### Core Interfaces

#### IStorageAdapter

```typescript
interface IStorageAdapter {
    // Basic operations
    getItem<T>(key: string): Promise<T | null>;
    setItem<T>(key: string, value: T, ttl?: number): Promise<void>;
    removeItem(key: string): Promise<void>;
    clear(): Promise<void>;
    
    // Metadata operations
    keys(): Promise<string[]>;
    length(): Promise<number>;
    
    // Health and maintenance
    isHealthy(): Promise<boolean>;
    cleanup(): Promise<number>;
    getCapabilities(): StorageCapabilities;
}
```

#### StorageConfig

```typescript
interface StorageConfig {
    name: string;           // Database name
    storeName: string;      // Store name within database
    version?: number;       // Schema version
    description?: string;   // Human-readable description
    platformOptions?: Record<string, any>; // Platform-specific options
}
```

### Storage Manager

```typescript
import { getStorageManager } from '@/lib/storage';

const manager = getStorageManager();

// Get storage for different stores
const userStorage = await manager.getStorage('users');
const sessionStorage = await manager.getStorage('sessions', 3600000); // 1 hour TTL

// Health monitoring
const health = await manager.getHealthStatus();
console.log('Storage healthy:', health.healthy);

// Cleanup expired items across all stores
const cleanupResults = await manager.performGlobalCleanup();
```

### Platform Detection

```typescript
import { detectPlatform, getRecommendedStorageAdapter, getReactNativeInfo } from '@/lib/storage';

const platform = detectPlatform();
console.log('Platform:', platform.platform); // 'browser', 'node', 'react-native', 'unknown'
console.log('Is browser:', platform.isBrowser);
console.log('Is server:', platform.isServer);

const recommendedAdapter = getRecommendedStorageAdapter();
console.log('Recommended adapter:', recommendedAdapter);
```

### React Native Version Detection

The storage system includes robust React Native version detection with multiple fallback strategies:

```typescript
import { getReactNativeInfo, isReactNative } from '@/lib/storage';

// Basic React Native detection
if (isReactNative()) {
    console.log('Running in React Native environment');
}

// Detailed React Native information
const rnInfo = getReactNativeInfo();
if (rnInfo) {
    console.log('React Native Version:', rnInfo.version);
    console.log('JavaScript Engine:', rnInfo.engine); // 'hermes', 'jsc', 'v8', etc.
    console.log('Bundler:', rnInfo.bundler); // 'metro', 'webpack', etc.
    console.log('Development Mode:', rnInfo.isDevelopment);
    console.log('Available Capabilities:', rnInfo.capabilities);
}
```

#### Version Detection Strategies

The system attempts to detect React Native version using multiple strategies:

1. **Platform Module Constants**: Checks `Platform.constants.reactNativeVersion`
2. **Package.json Version**: Attempts to read from `react-native/package.json`
3. **Global Constants**: Checks for global version variables and Hermes engine info
4. **User Agent Parsing**: Extracts version information from `navigator.userAgent`
5. **Environment Detection**: Detects development/production mode and available APIs

#### Version Format Examples

- `0.72.0` - Direct version from Platform module
- `package-0.72.0` - Version from package.json
- `hermes-0.12.0` - Hermes engine version
- `useragent-0.72.0` - Version parsed from user agent
- `env-dev-hermes-metro` - Environment-based detection
- `detected-unknown-version` - React Native detected but version unknown

#### Capability Detection

The system also detects available React Native capabilities:

```typescript
const rnInfo = getReactNativeInfo();
if (rnInfo) {
    const hasAsyncStorage = rnInfo.capabilities.includes('async-storage');
    const hasGeolocation = rnInfo.capabilities.includes('geolocation');
    const hasCamera = rnInfo.capabilities.includes('camera');

    console.log('AsyncStorage available:', hasAsyncStorage);
    console.log('Geolocation available:', hasGeolocation);
    console.log('Camera available:', hasCamera);
}
```

## Migration Guide

### From localStorage

**Before:**
```typescript
// Old localStorage usage
localStorage.setItem('user_data', JSON.stringify(userData));
const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
localStorage.removeItem('user_data');
```

**After:**
```typescript
// New storage abstraction
const storage = await getRepositoryStorage('user_data');
await storage.setItem('user_data', userData);
const userData = await storage.getItem('user_data');
await storage.removeItem('user_data');
```

### From localforage

**Before:**
```typescript
// Old localforage usage
import localforage from 'localforage';

const store = localforage.createInstance({ name: 'myDB', storeName: 'myStore' });
await store.setItem('key', value);
const value = await store.getItem('key');
```

**After:**
```typescript
// New storage abstraction
const storage = await getRepositoryStorage('myStore');
await storage.setItem('key', value);
const value = await storage.getItem('key');
```

### Repository Migration

**Before:**
```typescript
class OldRepository {
    private store = localforage.createInstance({ storeName: 'auth' });
    
    async getToken(): Promise<string | null> {
        return await this.store.getItem('token');
    }
}
```

**After:**
```typescript
class NewRepository extends RepositoryBase {
    constructor() {
        super('auth_store');
    }
    
    async getToken(): Promise<string | null> {
        return await this.getItem('token');
    }
}
```

## Advanced Usage

### Custom Storage Adapter

```typescript
import { BaseStorageAdapter, StorageCapabilities } from '@/lib/storage';

class CustomStorageAdapter extends BaseStorageAdapter {
    protected async initializeStorage(): Promise<void> {
        // Custom initialization logic
    }
    
    protected async getStoredItem<T>(key: string): Promise<StoredItemWithTimestamp<T> | null> {
        // Custom get implementation
    }
    
    // Implement other required methods...
    
    getCapabilities(): StorageCapabilities {
        return {
            platform: 'custom',
            persistent: true,
            supportsTTL: true,
            supportsBulkOperations: false,
            maxStorageSize: -1
        };
    }
}
```

### Testing

```typescript
import { createTestStorage } from '@/lib/storage';

describe('My Component', () => {
    let testStorage: IStorageAdapter;
    
    beforeEach(async () => {
        testStorage = await createTestStorage('test_store');
    });
    
    it('should store data correctly', async () => {
        await testStorage.setItem('test_key', 'test_value');
        const value = await testStorage.getItem('test_key');
        expect(value).toBe('test_value');
    });
});
```

## Configuration

### Environment-Specific Configuration

```typescript
// Browser-specific configuration
await initializeStorage({
    name: 'myApp',
    platformOptions: {
        driver: [localforage.INDEXEDDB, localforage.LOCALSTORAGE]
    }
});

// Node.js-specific configuration
await initializeStorage({
    name: 'myApp',
    platformOptions: {
        storageDir: '/path/to/storage'
    }
});
```

### TTL Configuration

```typescript
// Global TTL for all items in a store
const storage = await getRepositoryStorage('cache', 3600000); // 1 hour

// Per-item TTL
await storage.setItem('temp_data', data, 300000); // 5 minutes
```

## Best Practices

1. **Initialize Early**: Call `initializeStorage()` at application startup
2. **Use Repository Pattern**: Extend `RepositoryBase` for data access layers
3. **Handle Errors**: Always wrap storage operations in try-catch blocks
4. **Use TTL**: Set appropriate TTL for cached or temporary data
5. **Monitor Health**: Regularly check storage health in production
6. **Cleanup Regularly**: Run cleanup operations to remove expired items

## Troubleshooting

### Common Issues

1. **Storage Not Initialized**: Ensure `initializeStorage()` is called before using storage
2. **Platform Detection**: Check platform detection if storage behaves unexpectedly
3. **TTL Not Working**: Verify TTL is set correctly and cleanup is running
4. **Performance Issues**: Use appropriate storage adapter for your platform

### Debug Mode

```typescript
import { getStorageHealth } from '@/lib/storage';

const health = await getStorageHealth();
console.log('Storage Health:', health);

// Check individual adapter capabilities
const storage = await getRepositoryStorage('debug');
const capabilities = storage.getCapabilities();
console.log('Storage Capabilities:', capabilities);
```

## Performance Considerations

- **Browser**: IndexedDB is preferred over localStorage for large datasets
- **Node.js**: File system operations are atomic but may be slower for frequent writes
- **React Native**: AsyncStorage has size limitations, use cleanup regularly
- **Memory**: Fast but not persistent, suitable for testing and temporary data

## Security Considerations

- Data is stored unencrypted by default
- Implement encryption at the application layer if needed
- Be mindful of sensitive data in browser storage (accessible via DevTools)
- Use appropriate TTL for sensitive temporary data
