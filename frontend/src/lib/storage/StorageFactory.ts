/**
 * Storage factory for creating appropriate storage adapters based on platform.
 * 
 * This factory automatically detects the runtime environment and creates
 * the most suitable storage adapter for the platform.
 */

import { logger } from '@/lib/logger';
import { detectPlatform, getRecommendedStorageAdapter } from './platformDetection';
import type { IStorageAdapter, IStorageAdapterFactory, StorageConfig } from './types';

// Import adapters that are always available
import { MemoryStorageAdapter } from './adapters/MemoryStorageAdapter';

/**
 * Storage adapter factory implementation
 */
export class StorageAdapterFactory implements IStorageAdapterFactory {
    private static instance: StorageAdapterFactory | null = null;

    /**
     * Get singleton instance of the factory
     */
    public static getInstance(): StorageAdapterFactory {
        if (!StorageAdapterFactory.instance) {
            StorageAdapterFactory.instance = new StorageAdapterFactory();
        }
        return StorageAdapterFactory.instance;
    }

    /**
     * Create a storage adapter for the current platform
     */
    async createAdapter(config: StorageConfig): Promise<IStorageAdapter> {
        const adapterType = this.getAdapterType();
        
        logger.debug(`Creating storage adapter: ${adapterType} for store: ${config.storeName}`);

        let adapter: IStorageAdapter;

        switch (adapterType) {
            case 'browser':
                const { BrowserStorageAdapter } = await import('./adapters/BrowserStorageAdapter');
                adapter = new BrowserStorageAdapter();
                break;

            case 'node':
                // Dynamic import for Node.js adapter to avoid webpack issues
                const { NodeStorageAdapter } = await import('./adapters/NodeStorageAdapter');
                adapter = new NodeStorageAdapter();
                break;

            case 'react-native':
                // Dynamic import for React Native adapter to avoid webpack issues
                const { ReactNativeStorageAdapter } = await import('./adapters/ReactNativeStorageAdapter');
                adapter = new ReactNativeStorageAdapter();
                break;

            case 'memory':
            default:
                adapter = new MemoryStorageAdapter();
                break;
        }

        try {
            await adapter.initialize(config);
            logger.info(`Storage adapter initialized successfully: ${adapterType}`);
            return adapter;
        } catch (error) {
            logger.error(`Failed to initialize storage adapter: ${adapterType}`, error);
            
            // Fallback to memory storage if the preferred adapter fails
            if (adapterType !== 'memory') {
                logger.warn('Falling back to memory storage adapter');
                const fallbackAdapter = new MemoryStorageAdapter();
                await fallbackAdapter.initialize(config);
                return fallbackAdapter;
            }
            
            throw error;
        }
    }

    /**
     * Get the best available adapter for the current platform
     */
    getAdapterType(): string {
        return getRecommendedStorageAdapter();
    }

    /**
     * Check if a specific adapter type is available
     */
    isAdapterAvailable(adapterType: string): boolean {
        const platform = detectPlatform();

        switch (adapterType) {
            case 'browser':
                return platform.isBrowser;

            case 'node':
                return platform.isServer && platform.platform === 'node';

            case 'react-native':
                return platform.isReactNative;

            case 'memory':
                return true; // Always available

            default:
                return false;
        }
    }

    /**
     * Create a specific adapter type (for testing or explicit requirements)
     */
    async createSpecificAdapter(
        adapterType: string, 
        config: StorageConfig
    ): Promise<IStorageAdapter> {
        if (!this.isAdapterAvailable(adapterType)) {
            throw new Error(`Storage adapter '${adapterType}' is not available in this environment`);
        }

        let adapter: IStorageAdapter;

        switch (adapterType) {
            case 'browser':
                adapter = new BrowserStorageAdapter();
                break;

            case 'node':
                // Dynamic import for Node.js adapter to avoid webpack issues
                const { NodeStorageAdapter } = await import('./adapters/NodeStorageAdapter');
                adapter = new NodeStorageAdapter();
                break;

            case 'react-native':
                // Dynamic import for React Native adapter to avoid webpack issues
                const { ReactNativeStorageAdapter } = await import('./adapters/ReactNativeStorageAdapter');
                adapter = new ReactNativeStorageAdapter();
                break;

            case 'memory':
                adapter = new MemoryStorageAdapter();
                break;

            default:
                throw new Error(`Unknown storage adapter type: ${adapterType}`);
        }

        await adapter.initialize(config);
        return adapter;
    }

    /**
     * Get all available adapter types for the current platform
     */
    getAvailableAdapterTypes(): string[] {
        const types: string[] = [];

        if (this.isAdapterAvailable('browser')) {
            types.push('browser');
        }

        if (this.isAdapterAvailable('node')) {
            types.push('node');
        }

        if (this.isAdapterAvailable('react-native')) {
            types.push('react-native');
        }

        // Memory is always available
        types.push('memory');

        return types;
    }

    /**
     * Create an adapter with automatic fallback chain
     */
    async createAdapterWithFallback(
        config: StorageConfig,
        preferredTypes?: string[]
    ): Promise<IStorageAdapter> {
        const typesToTry = preferredTypes || [this.getAdapterType(), 'memory'];

        for (const adapterType of typesToTry) {
            if (this.isAdapterAvailable(adapterType)) {
                try {
                    return await this.createSpecificAdapter(adapterType, config);
                } catch (error) {
                    logger.warn(`Failed to create ${adapterType} adapter, trying next option:`, error);
                }
            }
        }

        throw new Error('No storage adapter could be created');
    }
}

/**
 * Convenience function to create a storage adapter
 */
export async function createStorageAdapter(config: StorageConfig): Promise<IStorageAdapter> {
    const factory = StorageAdapterFactory.getInstance();
    return await factory.createAdapter(config);
}

/**
 * Convenience function to create a specific storage adapter
 */
export async function createSpecificStorageAdapter(
    adapterType: string,
    config: StorageConfig
): Promise<IStorageAdapter> {
    const factory = StorageAdapterFactory.getInstance();
    return await factory.createSpecificAdapter(adapterType, config);
}

/**
 * Get the default storage factory instance
 */
export function getStorageFactory(): IStorageAdapterFactory {
    return StorageAdapterFactory.getInstance();
}
