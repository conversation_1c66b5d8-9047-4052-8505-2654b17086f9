/**
 * Storage manager for high-level storage operations and dependency injection.
 * 
 * This manager provides a centralized way to manage multiple storage instances,
 * handle dependency injection, and provide storage services to the application.
 */

import { logger } from '@/lib/logger';
import { StorageAdapterFactory } from './StorageFactory';
import type { IStorageAdapter, IStorageManager, StorageConfig } from './types';

/**
 * Storage manager implementation
 */
export class StorageManager implements IStorageManager {
    private static instance: StorageManager | null = null;
    private adapters: Map<string, IStorageAdapter> = new Map();
    private factory: StorageAdapterFactory;
    private globalConfig: Partial<StorageConfig> = {};
    private initialized = false;

    constructor() {
        this.factory = StorageAdapterFactory.getInstance();
    }

    /**
     * Get singleton instance of the storage manager
     */
    public static getInstance(): StorageManager {
        if (!StorageManager.instance) {
            StorageManager.instance = new StorageManager();
        }
        return StorageManager.instance;
    }

    /**
     * Initialize the storage manager with global configuration
     */
    async initialize(config: Partial<StorageConfig>): Promise<void> {
        this.globalConfig = { ...config };
        this.initialized = true;
        
        logger.info('Storage manager initialized', {
            name: config.name,
            version: config.version
        });
    }

    /**
     * Get a storage instance for a specific store
     */
    async getStorage(storeName: string, ttl?: number): Promise<IStorageAdapter> {
        if (!this.initialized) {
            throw new Error('Storage manager not initialized. Call initialize() first.');
        }

        // Check if we already have an adapter for this store
        if (this.adapters.has(storeName)) {
            return this.adapters.get(storeName)!;
        }

        // Create configuration for this store
        const storeConfig: StorageConfig = {
            name: this.globalConfig.name || 'infinityAppDB',
            storeName,
            version: this.globalConfig.version,
            description: this.globalConfig.description,
            platformOptions: {
                ...this.globalConfig.platformOptions,
                ttl // Store-specific TTL
            }
        };

        try {
            // Create and cache the adapter
            const adapter = await this.factory.createAdapter(storeConfig);
            this.adapters.set(storeName, adapter);
            
            logger.debug(`Storage adapter created for store: ${storeName}`);
            return adapter;
        } catch (error) {
            logger.error(`Failed to create storage adapter for store: ${storeName}`, error);
            throw error;
        }
    }

    /**
     * Get a storage instance with specific adapter type
     */
    async getStorageWithAdapter(
        storeName: string, 
        adapterType: string, 
        ttl?: number
    ): Promise<IStorageAdapter> {
        if (!this.initialized) {
            throw new Error('Storage manager not initialized. Call initialize() first.');
        }

        const cacheKey = `${storeName}:${adapterType}`;

        // Check if we already have an adapter for this store and type
        if (this.adapters.has(cacheKey)) {
            return this.adapters.get(cacheKey)!;
        }

        // Create configuration for this store
        const storeConfig: StorageConfig = {
            name: this.globalConfig.name || 'infinityAppDB',
            storeName,
            version: this.globalConfig.version,
            description: this.globalConfig.description,
            platformOptions: {
                ...this.globalConfig.platformOptions,
                ttl
            }
        };

        try {
            // Create specific adapter type
            const adapter = await this.factory.createSpecificAdapter(adapterType, storeConfig);
            this.adapters.set(cacheKey, adapter);
            
            logger.debug(`Storage adapter created for store: ${storeName} with type: ${adapterType}`);
            return adapter;
        } catch (error) {
            logger.error(`Failed to create ${adapterType} adapter for store: ${storeName}`, error);
            throw error;
        }
    }

    /**
     * Cleanup all storage instances
     */
    async cleanup(): Promise<void> {
        logger.info('Cleaning up storage manager...');

        const cleanupPromises: Promise<void>[] = [];

        for (const [storeName, adapter] of this.adapters) {
            cleanupPromises.push(
                adapter.cleanup().then(() => {
                    logger.debug(`Cleaned up storage adapter: ${storeName}`);
                }).catch(error => {
                    logger.warn(`Failed to cleanup storage adapter: ${storeName}`, error);
                })
            );
        }

        await Promise.all(cleanupPromises);

        // Dispose all adapters
        const disposePromises: Promise<void>[] = [];
        for (const [storeName, adapter] of this.adapters) {
            disposePromises.push(
                adapter.dispose().then(() => {
                    logger.debug(`Disposed storage adapter: ${storeName}`);
                }).catch(error => {
                    logger.warn(`Failed to dispose storage adapter: ${storeName}`, error);
                })
            );
        }

        await Promise.all(disposePromises);

        // Clear the adapters map
        this.adapters.clear();
        
        logger.info('Storage manager cleanup completed');
    }

    /**
     * Get storage health status
     */
    async getHealthStatus(): Promise<{
        healthy: boolean;
        adapters: Array<{
            storeName: string;
            healthy: boolean;
            error?: string;
        }>;
    }> {
        const adapterStatuses: Array<{
            storeName: string;
            healthy: boolean;
            error?: string;
        }> = [];

        let overallHealthy = true;

        for (const [storeName, adapter] of this.adapters) {
            try {
                const healthy = await adapter.isHealthy();
                adapterStatuses.push({
                    storeName,
                    healthy
                });

                if (!healthy) {
                    overallHealthy = false;
                }
            } catch (error) {
                adapterStatuses.push({
                    storeName,
                    healthy: false,
                    error: String(error)
                });
                overallHealthy = false;
            }
        }

        return {
            healthy: overallHealthy,
            adapters: adapterStatuses
        };
    }

    /**
     * Get all active storage instances
     */
    getActiveStorages(): Array<{ storeName: string; adapter: IStorageAdapter }> {
        return Array.from(this.adapters.entries()).map(([storeName, adapter]) => ({
            storeName,
            adapter
        }));
    }

    /**
     * Remove a specific storage instance
     */
    async removeStorage(storeName: string): Promise<void> {
        const adapter = this.adapters.get(storeName);
        if (adapter) {
            try {
                await adapter.dispose();
            } catch (error) {
                logger.warn(`Failed to dispose storage adapter: ${storeName}`, error);
            }
            this.adapters.delete(storeName);
            logger.debug(`Removed storage adapter: ${storeName}`);
        }
    }

    /**
     * Clear all data from a specific storage instance
     */
    async clearStorage(storeName: string): Promise<void> {
        const adapter = this.adapters.get(storeName);
        if (adapter) {
            await adapter.clear();
            logger.info(`Cleared storage: ${storeName}`);
        } else {
            logger.warn(`Storage not found: ${storeName}`);
        }
    }

    /**
     * Get storage usage information for all instances
     */
    async getUsageInfo(): Promise<Array<{
        storeName: string;
        usage: { used: number; available: number; total: number };
        capabilities: any;
    }>> {
        const usageInfo: Array<{
            storeName: string;
            usage: { used: number; available: number; total: number };
            capabilities: any;
        }> = [];

        for (const [storeName, adapter] of this.adapters) {
            try {
                const usage = await adapter.getUsage();
                const capabilities = adapter.getCapabilities();
                
                usageInfo.push({
                    storeName,
                    usage,
                    capabilities
                });
            } catch (error) {
                logger.warn(`Failed to get usage info for storage: ${storeName}`, error);
            }
        }

        return usageInfo;
    }

    /**
     * Perform cleanup on all storage instances
     */
    async performGlobalCleanup(): Promise<{ [storeName: string]: number }> {
        const cleanupResults: { [storeName: string]: number } = {};

        for (const [storeName, adapter] of this.adapters) {
            try {
                const cleanedCount = await adapter.cleanup();
                cleanupResults[storeName] = cleanedCount;
            } catch (error) {
                logger.warn(`Failed to cleanup storage: ${storeName}`, error);
                cleanupResults[storeName] = -1;
            }
        }

        return cleanupResults;
    }

    /**
     * Check if the storage manager is initialized
     */
    isInitialized(): boolean {
        return this.initialized;
    }

    /**
     * Get the global configuration
     */
    getGlobalConfig(): Partial<StorageConfig> {
        return { ...this.globalConfig };
    }
}

/**
 * Get the default storage manager instance
 */
export function getStorageManager(): IStorageManager {
    return StorageManager.getInstance();
}

/**
 * Convenience function to get a storage instance
 */
export async function getStorage(storeName: string, ttl?: number): Promise<IStorageAdapter> {
    const manager = StorageManager.getInstance();
    return await manager.getStorage(storeName, ttl);
}
