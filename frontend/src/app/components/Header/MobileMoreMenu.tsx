'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { PreferencesRepository } from '@/app/repository/PreferencesRepository';

// Define Language interface and languages array directly or import from a shared file
interface Language {
    code: string;
    name: string;
    flag: string;
}

const languages: Language[] = [
    { code: 'en', name: 'English', flag: '🇬🇧' },
    { code: 'ta', name: 'தமிழ்', flag: '🇮🇳' }
];

interface MobileMoreMenuProps {
    isOpen: boolean;
    onClose: () => void;
    onAccountClick: () => void;
    isUserLoggedIn: boolean;
    userName?: string;
    onUserLogout: () => void;
    onUserLogin: () => void;
    isCustomerFacingMode: boolean;
}

export default function MobileMoreMenu({
    isOpen,
    onClose,
    onAccountClick,
    isUserLoggedIn,
    userName,
    onUserLogout,
    onUserLogin,
    isCustomerFacingMode
}: MobileMoreMenuProps) {
    const { t, i18n } = useTranslation();
    const router = useRouter();
    const [currentLang, setCurrentLang] = useState<Language>(languages[0]);

    useEffect(() => {
        const currentLangCode = i18n.language || 'en';
        const lang = languages.find(l => l.code === currentLangCode) || languages[0];
        setCurrentLang(lang);
    }, [i18n.language]);

    const handleLanguageChange = async (language: Language) => {
        setCurrentLang(language);
        i18n.changeLanguage(language.code);

        // Save language preference using storage abstraction
        try {
            const preferencesRepository = PreferencesRepository.getInstance();
            await preferencesRepository.savePreferredLanguage(language.code);
        } catch (error) {
            console.error('Failed to save language preference:', error);
        }

        // In a real implementation with locale-based routing:
        // router.push(`/${language.code}${pathname.substr(3)}`); // Keep this commented or adapt if needed
        onClose(); // Close the menu after selection
    };

    if (!isOpen) return null;

    return (
        <div className="md:hidden fixed inset-0 z-50 flex items-end">
            {/* Overlay */}
            <div className="fixed inset-0 bg-black/50 transition-opacity duration-300 ease-in-out" onClick={onClose}></div>

            {/* Bottom Sheet Panel */}
            <div className="relative w-full bg-white shadow-xl rounded-t-lg transition-transform duration-300 ease-in-out transform translate-y-0">
                {/* Header with Close Button */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-800">{t('header.mobileMoreMenu.title', 'More Options')}</h3>
                    <button onClick={onClose} className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100" aria-label={t('common.close', 'Close')}>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                {/* Menu Content */}
                <div className="p-4">
                    <div className="mb-4">
                        <p className="text-sm text-gray-600 mb-2">{t('header.mobileMoreMenu.languageSection.title', 'Select Language')}:</p>
                        <ul className="space-y-1">
                            {languages.map((language) => (
                                <li key={language.code}>
                                    <button
                                        className={`w-full flex items-center text-left px-3 py-3 rounded-md text-base ${currentLang.code === language.code
                                            ? 'bg-gray-100 text-gray-900 font-semibold ring-2 ring-gray-300'
                                            : 'text-gray-700 hover:bg-gray-50'
                                            }`}
                                        onClick={() => handleLanguageChange(language)}
                                    >
                                        <span>{language.name}</span>
                                        {currentLang.code === language.code && (
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-auto text-[var(--color-green-600)]" viewBox="0 0 20 20" fill="currentColor">
                                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                            </svg>
                                        )}
                                    </button>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Account Section - Conditionally render based on mode */}
                    {!isCustomerFacingMode && (
                        <>
                            <hr className="my-4 border-gray-200" />
                            <div className="mb-2">
                                {isUserLoggedIn ? (
                                    <>
                                        <button
                                            onClick={() => {
                                                onAccountClick();
                                                onClose(); // Close this menu to open account sheet
                                            }}
                                            className="w-full flex items-center text-left px-3 py-3 rounded-md text-base text-gray-700 hover:bg-gray-50 mb-2"
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                            <span>{userName || t('header.mobileMoreMenu.account', 'Account')}</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-auto text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                            </svg>
                                        </button>
                                        <button
                                            onClick={onUserLogout}
                                            className="w-full flex items-center text-left px-3 py-3 rounded-md text-base text-red-600 hover:bg-red-50 hover:text-red-700"
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                            </svg>
                                            {t('header.mobileMoreMenu.logoutButton', 'Logout')}
                                        </button>
                                    </>
                                ) : (
                                    <button
                                        onClick={onUserLogin}
                                        className="w-full flex items-center text-left px-3 py-3 rounded-md text-base text-gray-700 hover:bg-gray-50"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                        </svg>
                                        {t('header.mobileMoreMenu.loginButton', 'Login')}
                                    </button>
                                )}
                            </div>
                        </>
                    )}
                    {/* Add other links or options here if needed in the future */}
                    {/* e.g., About Us, Help, Settings (if not in Account sheet) */}
                </div>
            </div>
        </div>
    );
} 