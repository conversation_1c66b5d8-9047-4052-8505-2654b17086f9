'use client';

import { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { villageBoundaries, type LatLng } from '@/lib/polygonUtils';

interface MapSelectorProps {
    initialLocation?: { lat: number; lng: number };
    onLocationSelect: (location: { lat: number; lng: number }) => void;
    disabled?: boolean; // When true, prevents user interactions with the map
    showVillagePolygons?: boolean; // When true, displays village boundary polygons
}

export const defaultLocation = { lat: 11.09054343539144, lng: 78.82722375774354 };

export default function MapSelector({
    initialLocation,
    onLocationSelect,
    disabled = false,
    showVillagePolygons = false
}: MapSelectorProps) {
    const { t } = useTranslation();
    const mapRef = useRef<HTMLDivElement>(null);
    const disabledRef = useRef(disabled);
    const [map, setMap] = useState<google.maps.Map | null>(null);
    const [marker, setMarker] = useState<google.maps.Marker | null>(null);
    const [polygons, setPolygons] = useState<any[]>([]);
    const [showPolygons, setShowPolygons] = useState(showVillagePolygons);
    const [isScriptLoaded, setIsScriptLoaded] = useState(false);
    const [isMapInitialized, setIsMapInitialized] = useState(false);
    const [loadError, setLoadError] = useState<string | null>(null);
    const [isGettingUserLocation, setIsGettingUserLocation] = useState(false);
    const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);

    // Use provided initialLocation, then user's current location if available, then default
    const getInitialLocation = () => {
        if (initialLocation) return initialLocation;
        if (userLocation) return userLocation;
        return defaultLocation;
    };

    // Get user's current location
    const getUserCurrentLocation = async () => {
        if (!navigator.geolocation) {
            console.warn('Geolocation is not supported by this browser.');
            return;
        }


        setIsGettingUserLocation(true);

        navigator.geolocation.getCurrentPosition(
            (position) => {
                const currentLocation = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                setUserLocation(currentLocation);

                // Update map and marker if they already exist
                if (map && marker) {
                    map.panTo(currentLocation);
                    map.setZoom(16); // Zoom in a bit more for current location
                    marker.setPosition(currentLocation);
                    onLocationSelect(currentLocation);
                } else {
                    // If map isn't ready yet, still call onLocationSelect for the modal
                    onLocationSelect(currentLocation);
                }

                setIsGettingUserLocation(false);
            },
            (error) => {
                console.warn('Error getting user location:', error.message);
                setIsGettingUserLocation(false);
            },
            {
                enableHighAccuracy: true,
                timeout: 10000, // Increased timeout for better reliability
                maximumAge: 60000 // Allow cached location up to 1 minute old
            }
        );
    };

    // Get user's location on component mount if no initialLocation was provided
    useEffect(() => {
        if (initialLocation || userLocation || isGettingUserLocation) {
            // If an initialLocation was provided or we already have user location, don't try to get it
            return;
        }

        // Automatically get user location for new sessions (when no initialLocation is provided)
        getUserCurrentLocation();
    }, [initialLocation, userLocation, isGettingUserLocation]);

    // Auto-detect location when modal opens for new session (no initialLocation)
    useEffect(() => {
        if (!initialLocation && !userLocation && !isGettingUserLocation) {
            // Small delay to ensure component is fully mounted
            const timer = setTimeout(() => {
                getUserCurrentLocation();
            }, 100);

            return () => clearTimeout(timer);
        }
    }, []);

    // Get API key from environment variable or use a placeholder
    const getGoogleMapsApiKey = () => {
        // In development mode, we might not have the actual API key
        if (process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {
            return process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
        }

        // For development without an API key, we'll provide fallback behavior
        console.warn('Google Maps API key not found. Map will use a placeholder.');
        return '';
    };

    // Load Google Maps script
    useEffect(() => {
        if (typeof window !== 'undefined' && !isScriptLoaded) {
            const apiKey = getGoogleMapsApiKey();

            // If no API key is available, use a fallback behavior
            if (!apiKey) {
                // For development, we'll simulate the map with our placeholder
                setIsScriptLoaded(false);
                setLoadError('No API key available');
                return;
            }

            // Check if the script is already loaded
            if (!document.getElementById('google-maps-script')) {
                const script = document.createElement('script');
                script.id = 'google-maps-script';
                script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
                script.async = true;
                script.defer = true;
                script.onload = () => setIsScriptLoaded(true);
                script.onerror = () => {
                    setLoadError('Failed to load Google Maps');
                };
                document.head.appendChild(script);
            } else {
                setIsScriptLoaded(true);
            }
        }
    }, [isScriptLoaded]);

    // Initialize map when script is loaded and component is mounted
    useEffect(() => {
        if (isScriptLoaded && mapRef.current && !isMapInitialized && typeof google !== 'undefined') {
            try {
                const locationToUse = getInitialLocation();
                console.log('MapSelector: Initializing map with location:', locationToUse, 'disabled:', disabled);

                const newMap = new google.maps.Map(mapRef.current, {
                    center: locationToUse,
                    zoom: 15,
                    mapTypeControl: false,
                    streetViewControl: false,
                    fullscreenControl: false,
                });

                const newMarker = new google.maps.Marker({
                    position: locationToUse,
                    map: newMap,
                    draggable: !disabled,
                    title: disabled ? t('map.readOnly', 'Map is read-only') : t('map.dragMarker', 'Drag to set location')
                });

                // Add event listeners - always add them but check disabled state inside
                console.log('MapSelector: Adding event listeners, disabled:', disabled);

                // Add click event to the map
                newMap.addListener('click', (e: google.maps.MapMouseEvent) => {
                    const currentDisabled = disabledRef.current;
                    console.log('MapSelector: Map clicked, disabled:', currentDisabled);
                    if (!currentDisabled && e.latLng) {
                        const clickedLocation = {
                            lat: e.latLng.lat(),
                            lng: e.latLng.lng()
                        };
                        console.log('MapSelector: Updating marker position to:', clickedLocation);
                        newMarker.setPosition(clickedLocation);
                        onLocationSelect(clickedLocation);
                    }
                });

                // Add drag end event to the marker
                newMarker.addListener('dragend', () => {
                    const currentDisabled = disabledRef.current;
                    console.log('MapSelector: Marker dragged, disabled:', currentDisabled);
                    if (!currentDisabled) {
                        const position = newMarker.getPosition();
                        if (position) {
                            const newLocation = {
                                lat: position.lat(),
                                lng: position.lng()
                            };
                            console.log('MapSelector: Marker drag ended, new location:', newLocation);
                            onLocationSelect(newLocation);
                        }
                    }
                });

                setMap(newMap);
                setMarker(newMarker);
                setIsMapInitialized(true);

                // Notify about the initial location (only if not disabled)
                if (!disabled) {
                    console.log('MapSelector: Calling onLocationSelect with initial location:', locationToUse);
                    onLocationSelect(locationToUse);
                }

                // Create village polygons on the map
                createVillagePolygons(newMap);
            } catch (error) {
                console.error('Error initializing map:', error);
                setLoadError('Error initializing Google Maps');
            }
        }
    }, [isScriptLoaded, onLocationSelect, t, userLocation, isMapInitialized, disabled]);

    // Update polygons when showVillagePolygons changes
    useEffect(() => {
        if (map && isMapInitialized) {
            createVillagePolygons(map);
        }
    }, [showPolygons, map, isMapInitialized]);

    // Cleanup polygons on unmount
    useEffect(() => {
        return () => {
            polygons.forEach(polygon => {
                if (polygon && polygon.setMap) {
                    polygon.setMap(null);
                }
            });
        };
    }, [polygons]);

    // Update marker draggable state when disabled changes
    useEffect(() => {
        if (marker) {
            console.log('MapSelector: Updating marker draggable state, disabled:', disabled);
            try {
                // Update the marker's draggable state
                (marker as any).setDraggable(!disabled);
                (marker as any).setTitle(disabled ? t('map.readOnly', 'Map is read-only') : t('map.dragMarker', 'Drag to set location'));

                // Force update marker options if needed
                const currentOptions = (marker as any).get('draggable');
                if (currentOptions === disabled) {
                    // If the draggable state is opposite of what we want, force update
                    (marker as any).setOptions({
                        draggable: !disabled,
                        title: disabled ? t('map.readOnly', 'Map is read-only') : t('map.dragMarker', 'Drag to set location')
                    });
                }
            } catch (error) {
                console.warn('MapSelector: Error updating marker properties:', error);
                // If direct update fails, recreate the marker
                if (map) {
                    const currentPosition = marker.getPosition();
                    if (currentPosition) {
                        // Remove old marker
                        marker.setMap(null);

                        // Create new marker with correct properties
                        const newMarker = new google.maps.Marker({
                            position: currentPosition,
                            map: map,
                            draggable: !disabled,
                            title: disabled ? t('map.readOnly', 'Map is read-only') : t('map.dragMarker', 'Drag to set location')
                        });

                        // Re-add drag event listener
                        newMarker.addListener('dragend', () => {
                            const currentDisabled = disabledRef.current;
                            console.log('MapSelector: Marker dragged (recreated), disabled:', currentDisabled);
                            if (!currentDisabled) {
                                const position = newMarker.getPosition();
                                if (position) {
                                    const newLocation = {
                                        lat: position.lat(),
                                        lng: position.lng()
                                    };
                                    console.log('MapSelector: Marker drag ended (recreated), new location:', newLocation);
                                    onLocationSelect(newLocation);
                                }
                            }
                        });

                        setMarker(newMarker);
                    }
                }
            }
        }
    }, [disabled, marker, map, t, onLocationSelect]);

    // Update marker position when initialLocation changes
    useEffect(() => {
        if (marker && initialLocation) {
            marker.setPosition(initialLocation);
            if (map) {
                map.panTo(initialLocation);
            }
        }
    }, [initialLocation, marker, map]);

    // Keep disabled ref updated
    useEffect(() => {
        disabledRef.current = disabled;
    }, [disabled]);

    // Convert LatLng array to Google Maps LatLng array
    const convertToGoogleLatLng = (coords: LatLng[]) => {
        if (typeof google === 'undefined' || !google.maps) return [];
        return coords.map(coord => new google.maps.LatLng(coord.lat, coord.lng));
    };

    // Create village polygons on the map
    const createVillagePolygons = (mapInstance: any) => {
        if (!showPolygons || !mapInstance || typeof google === 'undefined' || !google.maps) return;

        // Clear existing polygons
        polygons.forEach(polygon => {
            if (polygon && polygon.setMap) {
                polygon.setMap(null);
            }
        });

        const newPolygons: any[] = [];
        const colors = [
            '#FF6B6B', // Red
            '#4ECDC4', // Teal  
            '#45B7D1', // Blue
            '#96CEB4', // Green
            '#FECA57', // Yellow
        ];

        Object.entries(villageBoundaries).forEach(([_villageName, boundary], index) => {
            const color = colors[index % colors.length];

            const polygon = new (google.maps as any).Polygon({
                paths: convertToGoogleLatLng(boundary),
                strokeColor: color,
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: color,
                fillOpacity: 0.15,
                map: mapInstance,
                clickable: false, // Purely visual - no interaction
            });

            // No event listeners - polygons are purely visual indicators
            newPolygons.push(polygon);
        });

        setPolygons(newPolygons);
    };

    // Fallback for no API key or error loading Google Maps - Simulated map
    if (loadError || !isScriptLoaded) {
        return (
            <div className="w-full h-64 bg-gray-100 rounded-md relative">
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                    {/* Simulated map with fallback functionality */}
                    <div className="text-center mb-4">
                        <p className="text-gray-600 mb-1">
                            {isGettingUserLocation
                                ? t('map.gettingLocation', 'Getting your location...')
                                : loadError || t('map.preview', 'Map preview')}
                        </p>
                        <p className="text-sm text-gray-500">
                            {initialLocation
                                ? `Current location: ${initialLocation.lat.toFixed(4)}, ${initialLocation.lng.toFixed(4)}`
                                : userLocation
                                    ? `Your location: ${userLocation.lat.toFixed(4)}, ${userLocation.lng.toFixed(4)}`
                                    : t('map.noLocation', 'No location selected')}
                        </p>
                    </div>

                    {/* Fallback controls to simulate location selection */}
                    <div className="flex space-x-2">
                        {!isGettingUserLocation && (
                            <>
                                <button
                                    className="px-3 py-1 bg-indigo-100 text-indigo-700 rounded-md text-sm"
                                    onClick={() => {
                                        toast.error(t('map.googleMapsUnavailable', 'Google Maps is not available. Please select location manually or configure API key.'), {
                                            position: "top-right",
                                            autoClose: 5000,
                                        });
                                    }}
                                >
                                    {t('map.useCurrentLocation', 'Use Current Location')}
                                </button>
                                <button
                                    className="px-3 py-1 bg-indigo-100 text-indigo-700 rounded-md text-sm"
                                    onClick={() => {
                                        toast.error(t('map.googleMapsUnavailable', 'Google Maps is not available. Please select location manually or configure API key.'), {
                                            position: "top-right",
                                            autoClose: 5000,
                                        });
                                    }}
                                >
                                    {t('map.selectRandom', 'Select Random Location')}
                                </button>
                            </>
                        )}
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className={`map-container w-full h-64 rounded-md overflow-hidden relative`}>
            {/* Subtle read-only indicator instead of blocking overlay */}
            {disabled && (
                <div className="absolute top-2 left-2 z-20 pointer-events-none">
                    <div className="bg-white bg-opacity-90 rounded-lg px-3 py-1 shadow-md border border-gray-200">
                        <div className="flex items-center space-x-2">
                            <svg className="h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                            <p className="text-xs text-gray-600">{t('map.readOnlyMode', 'Read-only')}</p>
                        </div>
                    </div>
                </div>
            )}

            {isGettingUserLocation && (
                <div className="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-10">
                    <div className="bg-white rounded-lg p-2 shadow-md">
                        <p className="text-indigo-700">{t('map.gettingLocation', 'Getting your location...')}</p>
                    </div>
                </div>
            )}
            <div ref={mapRef} className="w-full h-full"></div>

            {/* Location controls - hide when disabled */}
            {!disabled && (
                <div className="absolute top-2 right-2 z-10 flex flex-col space-y-2">
                    {/* Village Polygons Toggle */}
                    {showPolygons && (
                        <button
                            onClick={() => setShowPolygons(!showPolygons)}
                            className={`bg-white p-2 rounded-full shadow-md focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200 hover:bg-gray-100 hover:shadow-lg ${showPolygons ? 'ring-2 ring-indigo-400' : ''
                                }`}
                            title={showPolygons ? t('map.hideVillagePolygons', 'Hide village boundaries') : t('map.showVillagePolygons', 'Show village boundaries')}
                        >
                            <svg className={`h-5 w-5 transition-colors duration-200 ${showPolygons ? 'text-indigo-700' : 'text-gray-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                            </svg>
                        </button>
                    )}

                    {/* Get Current Location Button */}
                    <button
                        onClick={getUserCurrentLocation}
                        disabled={isGettingUserLocation}
                        className={`bg-white p-2 rounded-full shadow-md focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200 ${isGettingUserLocation
                            ? 'bg-indigo-50 cursor-not-allowed'
                            : 'hover:bg-gray-100 hover:shadow-lg'
                            }`}
                        title={isGettingUserLocation ? t('map.gettingLocation', 'Getting your location...') : t('map.myLocation', 'Get my location')}
                    >
                        {isGettingUserLocation ? (
                            /* Loading spinner */
                            <svg className="h-5 w-5 text-indigo-700 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        ) : (
                            /* Material Design Triangle Location Arrow (Navigation Icon) */
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-700" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2L4.5 20.29l.71.71L12 18l6.79 3 .71-.71L12 2z" />
                            </svg>
                        )}
                    </button>
                </div>
            )}
        </div>
    );
} 