"use client";

import React, { useEffect, useState } from 'react';
import Modal from 'react-modal';
import { useTranslation } from 'react-i18next';
import { CheckCircleIcon } from '@heroicons/react/24/outline';
import Confetti from 'react-confetti';

interface OrderSuccessModalProps {
    isOpen: boolean;
    onClose: () => void;
    orderDetails?: {
        total: number;
        customerName: string;
    };
}

const OrderSuccessModal: React.FC<OrderSuccessModalProps> = ({
    isOpen,
    onClose,
    orderDetails
}) => {
    const { t } = useTranslation();
    const [showConfetti, setShowConfetti] = useState(false);
    const [windowDimensions, setWindowDimensions] = useState({ width: 0, height: 0 });

    // Get window dimensions for confetti
    useEffect(() => {
        const updateWindowDimensions = () => {
            setWindowDimensions({
                width: window.innerWidth,
                height: window.innerHeight
            });
        };

        if (typeof window !== 'undefined') {
            updateWindowDimensions();
            window.addEventListener('resize', updateWindowDimensions);
            return () => window.removeEventListener('resize', updateWindowDimensions);
        }
    }, []);

    // Show confetti when modal opens
    useEffect(() => {
        if (isOpen) {
            setShowConfetti(true);
            // Stop confetti after 5 seconds
            const timer = setTimeout(() => {
                setShowConfetti(false);
            }, 5000);
            return () => clearTimeout(timer);
        } else {
            setShowConfetti(false);
        }
    }, [isOpen]);

    // Auto-close modal after 10 seconds
    useEffect(() => {
        if (isOpen) {
            const timer = setTimeout(() => {
                onClose();
            }, 10000);
            return () => clearTimeout(timer);
        }
    }, [isOpen, onClose]);

    const customModalStyles = {
        overlay: {
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            backdropFilter: 'blur(4px)',
            zIndex: 200, // Higher than checkout modal
        },
        content: {
            top: '50%',
            left: '50%',
            right: 'auto',
            bottom: 'auto',
            marginRight: '-50%',
            transform: 'translate(-50%, -50%)',
            border: 'none',
            background: 'transparent',
            padding: '0',
            borderRadius: '0',
            width: '100%',
            maxWidth: '28rem',
            overflow: 'visible'
        },
    };

    if (!isOpen) return null;

    return (
        <>
            {/* Confetti Animation */}
            {showConfetti && (
                <div style={{ position: 'fixed', top: 0, left: 0, width: '100%', height: '100%', pointerEvents: 'none', zIndex: 9999 }}>
                    <Confetti
                        width={windowDimensions.width}
                        height={windowDimensions.height}
                        recycle={false}
                        numberOfPieces={200}
                        gravity={0.3}
                        colors={['#10B981', '#059669', '#047857', '#065F46', '#064E3B']}
                    />
                </div>
            )}

            <Modal
                isOpen={isOpen}
                onRequestClose={onClose}
                style={customModalStyles}
                contentLabel={t('order.success.title', 'Order Successful')}
                shouldCloseOnOverlayClick={true}
            >
                <div className="bg-white p-6 sm:p-8 rounded-lg shadow-xl w-full relative text-center">
                    {/* Success Icon */}
                    <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                        <CheckCircleIcon className="h-10 w-10 text-green-600" />
                    </div>

                    {/* Success Message */}
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">
                        {t('order.success.title', 'Order Successful!')}
                    </h2>

                    <p className="text-gray-600 mb-6">
                        {t('order.success.message', 'Thank you for your order.')}
                    </p>

                    {/* Order Details */}
                    {/* {orderDetails && (
                        <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
                            <h3 className="font-semibold text-gray-900 mb-3">
                                {t('orderSuccess.orderDetails', 'Order Details')}
                            </h3>

                            <div className="space-y-2 text-sm">
                                <div className="flex justify-between">
                                    <span className="text-gray-600">{t('orderSuccess.customer', 'Customer:')}</span>
                                    <span className="font-medium text-gray-900">{orderDetails.customerName}</span>
                                </div>

                                <div className="flex justify-between">
                                    <span className="text-gray-600">{t('orderSuccess.total', 'Total:')}</span>
                                    <span className="font-bold text-green-600">₹{orderDetails.total}</span>
                                </div>
                            </div>
                        </div>
                    )} */}

                    {/* Delivery Info */}
                    {/* <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <div className="flex items-center">
                            <svg className="h-5 w-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <p className="text-sm text-green-800">
                                {t('orderSuccess.deliveryInfo', 'Your order is being prepared and will be delivered soon!')}
                            </p>
                        </div>
                    </div> */}

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-3">
                        <button
                            onClick={onClose}
                            className="flex-1 bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                        >
                            {t('orderSuccess.continue', 'Continue Shopping')}
                        </button>

                        {/* <button
                            onClick={() => {
                                // TODO: Implement order tracking
                                console.log('Track order clicked');
                            }}
                            className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 font-semibold py-3 px-4 rounded-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                        >
                            {t('orderSuccess.trackOrder', 'Track Order')}
                        </button> */}
                    </div>

                    {/* Auto-close indicator */}
                    <p className="text-xs text-gray-500 mt-4">
                        {t('orderSuccess.autoClose', 'This dialog will close automatically in a few seconds')}
                    </p>
                </div>
            </Modal>
        </>
    );
};

export default OrderSuccessModal; 