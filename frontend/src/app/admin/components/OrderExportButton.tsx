'use client';

import React, { useState } from 'react';
import { ArrowDownTrayIcon } from '@heroicons/react/24/outline';
import { useTranslation } from 'react-i18next';

interface ExportButtonProps {
    onExport: (exportFiltered: boolean) => Promise<void>;
    filteredCount: number;
    totalCount: number;
    disabled?: boolean;
    entityName?: string; // e.g., 'orders', 'carts' 
}

const ExportButton: React.FC<ExportButtonProps> = ({
    onExport,
    filteredCount,
    totalCount,
    disabled = false,
    entityName = 'orders'
}) => {
    const { t } = useTranslation();
    const [isExporting, setIsExporting] = useState(false);
    const [showOptions, setShowOptions] = useState(false);
    const [exportFiltered, setExportFiltered] = useState(true);

    const handleExport = async () => {
        setIsExporting(true);
        try {
            await onExport(exportFiltered);
        } finally {
            setIsExporting(false);
            setShowOptions(false);
        }
    };

    const handleButtonClick = () => {
        if (filteredCount === totalCount) {
            // If no filters applied, export directly
            handleExport();
        } else {
            // Show options when filters are applied
            setShowOptions(!showOptions);
        }
    };

    return (
        <div className="relative">
            <button
                onClick={handleButtonClick}
                disabled={disabled || isExporting}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title={t(`admin.${entityName}.export.title`)}
            >
                <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
                {isExporting ? t(`admin.${entityName}.export.processing`) : t('admin.importExport.export')}
            </button>

            {/* Export Options Dropdown */}
            {showOptions && (
                <div className="absolute top-full right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                    <div className="p-4">
                        <h3 className="text-sm font-medium text-gray-900 mb-3">
                            {t(`admin.${entityName}.export.title`)}
                        </h3>

                        {/* Filtered Data Option */}
                        <label className="flex items-start space-x-3 mb-4 cursor-pointer">
                            <input
                                type="radio"
                                name="exportOption"
                                checked={exportFiltered}
                                onChange={() => setExportFiltered(true)}
                                className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                            />
                            <div className="flex-1">
                                <div className="text-sm font-medium text-gray-900">
                                    {t(`admin.${entityName}.export.filteredData`, { count: filteredCount })}
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                    {t(`admin.${entityName}.export.filteredDescription`)}
                                </div>
                            </div>
                        </label>

                        {/* All Data Option */}
                        <label className="flex items-start space-x-3 mb-4 cursor-pointer">
                            <input
                                type="radio"
                                name="exportOption"
                                checked={!exportFiltered}
                                onChange={() => setExportFiltered(false)}
                                className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                            />
                            <div className="flex-1">
                                <div className="text-sm font-medium text-gray-900">
                                    {t(`admin.${entityName}.export.allData`, { total: totalCount })}
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                    {t(`admin.${entityName}.export.allDescription`)}
                                </div>
                            </div>
                        </label>

                        {/* Action Buttons */}
                        <div className="flex items-center justify-end space-x-2">
                            <button
                                onClick={() => setShowOptions(false)}
                                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
                            >
                                {t('admin.actions.cancel')}
                            </button>
                            <button
                                onClick={handleExport}
                                disabled={isExporting}
                                className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                            >
                                {isExporting ? t(`admin.${entityName}.export.processing`) : t('admin.importExport.export')}
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Backdrop to close dropdown */}
            {showOptions && (
                <div
                    className="fixed inset-0 z-40"
                    onClick={() => setShowOptions(false)}
                />
            )}
        </div>
    );
};

export default ExportButton; 