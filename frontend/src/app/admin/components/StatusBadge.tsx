"use client";

import React from 'react';

interface StatusBadgeProps {
    status: 'active' | 'inactive' | 'pending' | 'confirmed' | 'cancelled' | 'completed' | string;
    size?: 'sm' | 'md' | 'lg';
    className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({
    status,
    size = 'md',
    className = ''
}) => {
    const getStatusConfig = (status: string) => {
        const statusLower = status.toLowerCase();

        switch (statusLower) {
            case 'active':
                return {
                    color: 'bg-green-100 text-green-800',
                    label: 'Active'
                };
            case 'inactive':
                return {
                    color: 'bg-gray-100 text-gray-800',
                    label: 'Inactive'
                };
            case 'pending':
                return {
                    color: 'bg-yellow-100 text-yellow-800',
                    label: 'Pending'
                };
            case 'confirmed':
                return {
                    color: 'bg-blue-100 text-blue-800',
                    label: 'Confirmed'
                };
            case 'cancelled':
                return {
                    color: 'bg-red-100 text-red-800',
                    label: 'Cancelled'
                };
            case 'completed':
                return {
                    color: 'bg-green-100 text-green-800',
                    label: 'Completed'
                };
            case 'in-progress':
            case 'in_progress':
                return {
                    color: 'bg-blue-100 text-blue-800',
                    label: 'In Progress'
                };
            case 'abandoned':
                return {
                    color: 'bg-orange-100 text-orange-800',
                    label: 'Abandoned'
                };
            case 'converted':
                return {
                    color: 'bg-purple-100 text-purple-800',
                    label: 'Converted'
                };
            default:
                return {
                    color: 'bg-gray-100 text-gray-800',
                    label: status.charAt(0).toUpperCase() + status.slice(1)
                };
        }
    };

    const getSizeClasses = (size: string) => {
        switch (size) {
            case 'sm':
                return 'px-2 py-0.5 text-xs';
            case 'lg':
                return 'px-3 py-1 text-sm';
            default:
                return 'px-2.5 py-0.5 text-xs';
        }
    };

    const config = getStatusConfig(status);
    const sizeClasses = getSizeClasses(size);

    return (
        <span
            className={`inline-flex items-center rounded-full font-medium ${config.color} ${sizeClasses} ${className}`}
        >
            {config.label}
        </span>
    );
};

export default StatusBadge; 