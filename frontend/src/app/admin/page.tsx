"use client";

import React, { useEffect } from 'react';
import Link from 'next/link';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { getOrderedCategories } from '../services/categoryService';
import { getCartsForAdmin } from '../services/cartService';
import { getOrdersForAdmin, formatDateForAPI } from '../services/orderService';
import { useAdminPermissions } from '../hooks/useAdminPermissions';
import { useAuth } from '../context/AuthContext';
import { logger } from '@/lib/logger';

/**
 * Gets date range for recent orders: yesterday + today
 */
const getRecentOrdersDateRange = (): { dateFrom: string; dateTo: string } => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    return {
        dateFrom: formatDateForAPI(yesterday), // Yesterday in YYYY-MM-DD
        dateTo: formatDateForAPI(today)        // Today in YYYY-MM-DD
    };
};

/**
 * Gets date range for recent carts in epoch seconds
 * From yesterday 00:00:00 to today 23:59:59
 */
const getRecentCartsDateRange = (): { dateFrom: string; dateTo: string } => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    // Set yesterday to 00:00:00
    yesterday.setHours(0, 0, 0, 0);

    // Set today to 23:59:59
    const todayEnd = new Date(today);
    todayEnd.setHours(23, 59, 59, 999);

    return {
        dateFrom: formatDateForAPI(yesterday), // Yesterday in YYYY-MM-DD
        dateTo: formatDateForAPI(todayEnd)     // Today in YYYY-MM-DD
    };
};

const AdminDashboard: React.FC = () => {
    const { t } = useTranslation();
    const { isUserLoggedIn } = useAuth();
    const { forceLoadAdminRoles, checkPermission, adminRoles, adminRolesChecked } = useAdminPermissions();

    // Load admin roles when component mounts (fallback mechanism)
    useEffect(() => {
        const loadRoles = async () => {
            if (isUserLoggedIn && !adminRoles && !adminRolesChecked) {
                logger.info('[AdminDashboard] Loading admin roles as fallback...');
                try {
                    await forceLoadAdminRoles();
                } catch (error) {
                    logger.error('[AdminDashboard] Failed to load admin roles:', error);
                }
            }
        };

        loadRoles();
    }, [isUserLoggedIn, adminRoles, adminRolesChecked, forceLoadAdminRoles]);

    // Fetch categories for dashboard stats
    const {
        data: categories,
        isLoading: isLoadingCategories,
    } = useQuery({
        queryKey: ['categories'],
        queryFn: async () => {
            try {
                // Direct service call with includeInactive for admin
                return await getOrderedCategories({ includeInactive: true });
            } catch (error) {
                logger.error('Error loading admin categories:', error);
                return [];
            }
        },
    });

    // Fetch recent carts count (CREATED status, yesterday + today)
    const {
        data: cartsData,
        isLoading: isLoadingCarts,
    } = useQuery({
        queryKey: ['recent-carts-count'],
        queryFn: async () => {
            try {
                const dateRange = getRecentCartsDateRange();
                // Fetch minimal data with server-side filtering to get totalRows for count
                return await getCartsForAdmin(1, 1, {
                    status: 'CREATED',
                    dateFrom: dateRange.dateFrom,
                    dateTo: dateRange.dateTo
                });
            } catch (error) {
                logger.error('Error loading recent carts count:', error);
                return null;
            }
        },
    });

    // Fetch recent orders (yesterday + today)
    const {
        data: recentOrders,
        isLoading: isLoadingOrders,
    } = useQuery({
        queryKey: ['recent-orders'],
        queryFn: async () => {
            try {
                const dateRange = getRecentOrdersDateRange();
                return await getOrdersForAdmin(dateRange.dateFrom, dateRange.dateTo);
            } catch (error) {
                logger.error('Error loading recent orders:', error);
                return [];
            }
        },
    });

    const stats = [
        {
            name: t('admin.dashboard.stats.totalCategories'),
            value: categories?.length || 0,
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
            ),
            color: 'bg-blue-500',
            loading: isLoadingCategories,
        },
        {
            name: t('admin.dashboard.stats.totalSkus'),
            value: categories?.reduce((total, cat) => total + cat.skuIds.length, 0) || 0,
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
            ),
            color: 'bg-green-500',
            loading: isLoadingCategories,
        },
        {
            name: t('admin.dashboard.stats.recentCarts'),
            value: cartsData?.pagination?.totalRows || '—',
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2-2v6z" />
                </svg>
            ),
            color: 'bg-yellow-500',
            loading: isLoadingCarts,
        },
        {
            name: t('admin.dashboard.stats.recentOrders'),
            value: recentOrders?.length || '—',
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
            ),
            color: 'bg-purple-500',
            loading: isLoadingOrders,
        },
    ];

    const quickActions = [
        {
            name: t('admin.dashboard.quickActions.createCategory.name'),
            description: t('admin.dashboard.quickActions.createCategory.description'),
            href: '/admin/categories/create',
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
            ),
            permission: 'manageCategories' as const,
            available: true,
        },
        {
            name: t('admin.dashboard.quickActions.createSku.name'),
            description: t('admin.dashboard.quickActions.createSku.description'),
            href: '/admin/skus/create',
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
            ),
            permission: 'manageSku' as const,
            available: true,
        },
        {
            name: t('admin.dashboard.quickActions.viewCategories.name'),
            description: t('admin.dashboard.quickActions.viewCategories.description'),
            href: '/admin/categories',
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
            ),
            permission: 'manageCategories' as const,
            available: true,
        },
        {
            name: t('admin.dashboard.quickActions.viewSkus.name'),
            description: t('admin.dashboard.quickActions.viewSkus.description'),
            href: '/admin/skus',
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
            ),
            permission: 'manageSku' as const,
            available: true,
        },
        {
            name: t('admin.dashboard.quickActions.viewCarts.name'),
            description: t('admin.dashboard.quickActions.viewCarts.description'),
            href: '/admin/cart',
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2-2v6z" />
                </svg>
            ),
            permission: 'viewAllCarts' as const,
            available: true,
        },
    ];

    return (
        <div className="space-y-4 sm:space-y-6">
            {/* Welcome Section - Mobile optimized */}
            <div className="bg-white rounded-lg shadow p-4 sm:p-6">
                <h2 className="text-lg sm:text-xl font-medium text-gray-900 mb-2">
                    {t('admin.dashboard.welcome.title')}
                </h2>
                <p className="text-sm sm:text-base text-gray-600">
                    {t('admin.dashboard.welcome.description')}
                </p>
            </div>

            {/* Stats Grid - Consistent Layout */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                {stats.map((stat) => (
                    <div key={stat.name} className="bg-white rounded-lg shadow p-4 sm:p-6">
                        <div className="flex items-center justify-between">
                            <div className="flex items-start">
                                <div className={`p-2 rounded-md ${stat.color} mr-3 flex-shrink-0`}>
                                    <div className="text-white w-5 h-5">{stat.icon}</div>
                                </div>
                                <div className="flex-1 min-w-0">
                                    <div className="text-sm font-medium text-gray-900 mb-1">{stat.name}</div>
                                    {(stat.name.includes('Recent') || stat.name.includes('சமீபத்திய')) && (
                                        <div className="text-xs text-gray-500">{t('admin.dashboard.stats.recentTimeframe')}</div>
                                    )}
                                    {!(stat.name.includes('Recent') || stat.name.includes('சமீபத்திய')) && (
                                        <div className="text-xs text-gray-500">&nbsp;</div>
                                    )}
                                </div>
                            </div>
                            <div className="text-right">
                                <div className="text-2xl font-bold text-gray-900">
                                    {stat.loading ? (
                                        <div className="animate-pulse bg-gray-200 h-8 w-12 rounded"></div>
                                    ) : (
                                        stat.value
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {/* Quick Actions - Touch Optimized */}
            <div className="bg-white rounded-lg shadow">
                <div className="px-4 py-3 sm:px-6 sm:py-4 border-b border-gray-200">
                    <h3 className="text-lg sm:text-xl font-medium text-gray-900">
                        {t('admin.dashboard.quickActions.title')}
                    </h3>
                </div>
                <div className="p-4 sm:p-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4">
                        {quickActions.map((action) => {
                            const hasPermission = checkPermission(action.permission);
                            const isDisabled = !hasPermission || !action.available;

                            return (
                                <div key={action.name}>
                                    {isDisabled ? (
                                        <div className="flex flex-col items-center p-4 sm:p-6 border-2 border-dashed border-gray-200 rounded-lg text-center cursor-not-allowed opacity-50 min-h-[120px] sm:min-h-[140px]">
                                            <div className="text-gray-400 mb-2 sm:mb-3">{action.icon}</div>
                                            <div className="text-sm font-medium text-gray-400 mb-1">{action.name}</div>
                                            <div className="text-xs text-gray-400 mb-2">{action.description}</div>
                                            {!action.available && (
                                                <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-600">
                                                    {t('admin.dashboard.quickActions.comingSoon')}
                                                </span>
                                            )}
                                        </div>
                                    ) : (
                                        <Link
                                            href={action.href}
                                            className="flex flex-col items-center p-4 sm:p-6 border-2 border-dashed border-gray-300 rounded-lg text-center hover:border-blue-500 hover:bg-blue-50 transition-colors group min-h-[120px] sm:min-h-[140px] active:scale-95 transform touch-manipulation"
                                        >
                                            <div className="text-gray-600 group-hover:text-blue-600 mb-2 sm:mb-3">{action.icon}</div>
                                            <div className="text-sm font-medium text-gray-900 group-hover:text-blue-900 mb-1">{action.name}</div>
                                            <div className="text-xs text-gray-500 group-hover:text-blue-700">{action.description}</div>
                                        </Link>
                                    )}
                                </div>
                            );
                        })}
                    </div>
                </div>
            </div>

            {/* Recent Activity Placeholder - Mobile Optimized */}
            <div className="bg-white rounded-lg shadow">
                <div className="px-4 py-3 sm:px-6 sm:py-4 border-b border-gray-200">
                    <h3 className="text-lg sm:text-xl font-medium text-gray-900">
                        {t('admin.dashboard.recentActivity.title')}
                    </h3>
                </div>
                <div className="p-4 sm:p-6">
                    <div className="text-center text-gray-500 py-6 sm:py-8">
                        <svg className="w-10 h-10 sm:w-12 sm:h-12 mx-auto text-gray-300 mb-3 sm:mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        <p className="text-sm sm:text-base">
                            {t('admin.dashboard.recentActivity.placeholder')}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AdminDashboard;