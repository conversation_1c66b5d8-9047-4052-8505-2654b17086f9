"use client";

import React, { useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { AdminGuard } from '../../components/common/AdminGuard';
import DataTable, { Column } from '../components/DataTable';
import StatusBadge from '../components/StatusBadge';
import {
    getOrdersForAdmin,
    calculateOrderTotal,
    formatDeliveryDate,
    getUniqueFacilityKeys,
    formatDateIST
} from '../../services/orderService';
import { Order, ORDER_STATUSES } from '../../types/order';
import { useOrderFilters } from '../../hooks/useOrderFilters';
import { useTranslation } from 'react-i18next';
import { logger } from '@/lib/logger';
import DatePicker from '../../components/common/DatePicker';
import { ArrowPathIcon } from '@heroicons/react/24/outline';
import ExportButton from '../components/OrderExportButton';
import { templateService } from '../../services/templateService';
import { excelService } from '../../services/excelService';
import { getSkus } from '../../services/skuService';
import { toast } from 'react-toastify';

const OrdersPage: React.FC = () => {
    const { t } = useTranslation();
    const router = useRouter();
    const { filters, updateFilter, clearStatusAndFacility } = useOrderFilters();
    const [isExporting, setIsExporting] = useState(false);

    // Fetch orders based on date range with all filters in query key
    const {
        data: orders = [],
        isLoading,
        isError,
        error,
        refetch,
        isFetching
    } = useQuery<Order[], Error>({
        queryKey: ['admin-orders', filters.dateFrom, filters.dateTo, filters.status, filters.facilityKey],
        queryFn: () => getOrdersForAdmin(filters.dateFrom, filters.dateTo),
        staleTime: 30000, // 30 seconds
        gcTime: 300000, // 5 minutes
        refetchOnMount: true,
        refetchOnWindowFocus: false,
    });

    // Get unique facility keys for filter dropdown
    const facilityKeys = useMemo(() => getUniqueFacilityKeys(orders), [orders]);

    // Filter orders based on status and facility
    const filteredOrders = useMemo(() => {
        return orders.filter(order => {
            const statusMatch = !filters.status || order.status === filters.status;
            const facilityMatch = !filters.facilityKey || order.facilityKey === filters.facilityKey;
            return statusMatch && facilityMatch;
        });
    }, [orders, filters.status, filters.facilityKey]);

    // Handle filter changes
    const handleDateRangeChange = (field: 'dateFrom' | 'dateTo', value: string) => {
        updateFilter(field, value);
    };

    const handleStatusChange = (status: string) => {
        updateFilter('status', status === 'all' ? '' : status);
    };

    const handleFacilityChange = (facilityKey: string) => {
        updateFilter('facilityKey', facilityKey === 'all' ? '' : facilityKey);
    };

    // Handle refresh button click
    const handleRefresh = () => {
        logger.debug('Orders: Manual refresh triggered', { filters });
        refetch();
    };

    // Handle row click - navigate to order details with preserved filters
    const handleRowClick = (order: Order) => {
        logger.debug('Order clicked', { orderId: order.id, currentFilters: filters });

        // Preserve current filters in URL when navigating to order detail
        const currentParams = new URLSearchParams();
        if (filters.dateFrom) currentParams.set('dateFrom', filters.dateFrom);
        if (filters.dateTo) currentParams.set('dateTo', filters.dateTo);
        if (filters.status) currentParams.set('status', filters.status);
        if (filters.facilityKey) currentParams.set('facilityKey', filters.facilityKey);

        const orderDetailURL = `/admin/orders/${order.id}?returnFilters=${encodeURIComponent(currentParams.toString())}`;
        router.push(orderDetailURL);
    };

    // Handle export functionality
    const handleExport = async (exportFiltered: boolean) => {
        setIsExporting(true);
        try {
            logger.debug('Orders export triggered', {
                exportFiltered,
                filteredCount: filteredOrders.length,
                totalCount: orders.length,
                filters
            });

            // Choose data to export
            const exportData = exportFiltered ? filteredOrders : orders;

            if (exportData.length === 0) {
                toast.error(t('admin.orders.export.error'));
                return;
            }

            // Step 1: Collect all unique SKU IDs from all orders
            const allSkuIds = new Set<number>();
            exportData.forEach(order => {
                if (order.skuItems && order.skuItems.length > 0) {
                    order.skuItems.forEach(item => {
                        allSkuIds.add(item.skuID);
                    });
                }
            });

            // Step 2: Batch fetch all SKUs at once (optimized)
            const skuLookupMap: Record<number, any> = {};
            if (allSkuIds.size > 0) {
                try {
                    const uniqueSkuIds = Array.from(allSkuIds);
                    logger.debug('Orders export: Batch fetching SKUs', {
                        uniqueSkuCount: uniqueSkuIds.length,
                        totalOrderItems: exportData.reduce((sum, order) => sum + (order.skuItems?.length || 0), 0)
                    });

                    const batchSkus = await getSkus({ skuIds: uniqueSkuIds, allowInactive: true });
                    batchSkus.forEach((sku: any) => {
                        skuLookupMap[sku.skuId] = sku;
                    });
                } catch (error) {
                    logger.warn('Orders export: Batch SKU fetch failed, will use fallback names', { error });
                }
            }

            // Step 3: Format orders for export using pre-fetched SKUs
            const formattedData = exportData.map((order) => {
                // Build SKU details string
                let skuDetails = '';
                let skuIds = '';

                if (order.skuItems && order.skuItems.length > 0) {
                    const skuDetailsArray = order.skuItems.map((item) => {
                        const sku = skuLookupMap[item.skuID];
                        const skuName = sku?.name?.en || `SKU#${item.skuID}`;
                        const unit = item.unit || 'unit';
                        const quantity = item.quantity || 0;
                        const price = item.price || 0;
                        return `SKU#${item.skuID}: ${skuName} ${unit} (Qty: ${quantity}, ₹${price} each)`;
                    });

                    skuDetails = skuDetailsArray.join(', ');
                    skuIds = order.skuItems.map(item => item.skuID).join(',');
                }

                return {
                    orderId: order.id,
                    status: order.status,
                    mobile: order.mobile || '',
                    facilityKey: order.facilityKey || '',
                    itemsCount: order.skuItems?.length || 0,
                    totalValue: calculateOrderTotal(order),
                    createdAt: order.createdAt ? formatDateIST(new Date(order.createdAt * 1000)) : '',
                    deliveryDate: formatDeliveryDate(order.deliveryDate),
                    skuDetails,
                    skuIds,
                    deliveryLocation: order.deliveryLocation || '',
                    orderComments: order.metadata?.orderComments || '',
                    deliveryInstructions: order.metadata?.deliveryInstruction || '',
                    createdBy: order.createdBy ? order.createdBy.toString() : '',
                };
            });

            // Get template for export
            const orderTemplate = templateService.getTemplate('order-list');
            if (!orderTemplate) {
                throw new Error('Order export template not found');
            }

            // Generate filename with filter info
            const dateStr = new Date().toISOString().split('T')[0];
            let filename = `orders_${dateStr}`;

            // Add filter info to filename
            if (filters.dateFrom && filters.dateTo) {
                const fromDate = filters.dateFrom.replace(/-/g, '');
                const toDate = filters.dateTo.replace(/-/g, '');
                filename = `orders_${fromDate}_to_${toDate}`;
            }

            if (exportFiltered && (filters.status || filters.facilityKey)) {
                if (filters.status) filename += `_${filters.status}`;
                if (filters.facilityKey) filename += `_${filters.facilityKey}`;
            }

            filename += '.csv';

            // Generate CSV
            await excelService.generateCSV(
                formattedData,
                orderTemplate.columns,
                filename,
                {
                    includeInstructions: true,
                    instructions: [
                        ...orderTemplate.instructions || [],
                        `Export date: ${new Date().toLocaleDateString('en-IN')}`,
                        `Date range: ${filters.dateFrom} to ${filters.dateTo}`,
                        exportFiltered
                            ? `Filtered data: ${filteredOrders.length} orders (Status: ${filters.status || 'All'}, Facility: ${filters.facilityKey || 'All'})`
                            : `All data: ${orders.length} orders in date range`
                    ]
                }
            );

            toast.success(t('admin.orders.export.success'));
            logger.info('Orders export completed successfully', {
                exportCount: formattedData.length,
                exportFiltered
            });

        } catch (error) {
            logger.error('Orders export failed', { error });
            toast.error(t('admin.orders.export.error'));
        } finally {
            setIsExporting(false);
        }
    };

    // Define table columns with proper sorting
    const columns: Column<Order>[] = useMemo(() => [
        {
            key: 'id',
            header: t('admin.orders.table.orderId'),
            width: '100px',
            render: (order) => `#${order.id}`,
            sortable: true,
            getSortValue: (order) => order.id
        },
        {
            key: 'status',
            header: t('admin.orders.table.status'),
            width: '120px',
            render: (order) => <StatusBadge status={order.status.toLowerCase()} />,
            sortable: true,
            getSortValue: (order) => order.status
        },
        {
            key: 'mobile',
            header: t('admin.orders.table.mobile'),
            width: '140px',
            render: (order) => order.mobile || t('admin.orders.table.noMobile'),
            sortable: true,
            getSortValue: (order) => order.mobile || ''
        },
        {
            key: 'itemsCount',
            header: t('admin.orders.table.itemsCount'),
            width: '100px',
            render: (order) => order.skuItems.length.toString(),
            sortable: true,
            getSortValue: (order) => order.skuItems.length
        },
        {
            key: 'totalValue',
            header: t('admin.orders.table.totalValue'),
            width: '120px',
            render: (order) => `₹${calculateOrderTotal(order).toFixed(2)}`,
            sortable: true,
            getSortValue: (order) => calculateOrderTotal(order)
        },
        {
            key: 'endCustomerName',
            header: t('admin.orders.table.endCustomerName'),
            width: '120px',
            render: (order) => order.endCustomerName || t('admin.orders.table.noEndCustomerName'),
            sortable: true,
            getSortValue: (order) => order.endCustomerName || ''
        },
        {
            key: 'createdBy',
            header: t('admin.orders.table.createdBy'),
            width: '120px',
            render: (order) => order.createdBy ? order.createdBy.toString() : '',
            sortable: true,
            getSortValue: (order) => order.createdBy || 0
        },
        {
            key: 'createdAt',
            header: t('admin.orders.table.createdAt'),
            width: '120px',
            render: (order) => order.createdAt ? formatDateIST(new Date(order.createdAt * 1000)) : '',
            sortable: true,
            getSortValue: (order) => order.createdAt || 0
        },
        {
            key: 'deliveryDate',
            header: t('admin.orders.table.deliveryDate'),
            width: '180px',
            render: (order) => formatDeliveryDate(order.deliveryDate),
            sortable: true,
            getSortValue: (order) => order.deliveryDate ? new Date(order.deliveryDate).getTime() : 0
        },
        {
            key: 'facilityKey',
            header: t('admin.orders.table.facility'),
            width: '120px',
            render: (order) => order.facilityKey || t('admin.orders.table.noFacility'),
            sortable: true,
            getSortValue: (order) => order.facilityKey || ''
        }
    ], [t]);

    if (isError) {
        return (
            <AdminGuard requiredPermission="viewAllOrders">
                <div className="p-6">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 className="text-lg font-medium text-red-800 mb-2">
                            {t('admin.orders.error.title')}
                        </h3>
                        <p className="text-red-600">
                            {error?.message || t('admin.orders.error.generic')}
                        </p>
                        <button
                            onClick={() => refetch()}
                            className="mt-3 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                        >
                            {t('admin.orders.error.retry')}
                        </button>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    return (
        <AdminGuard requiredPermission="viewAllOrders">
            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                            {t('admin.orders.title')}
                        </h1>
                        <p className="text-gray-600 mt-1">
                            {t('admin.orders.subtitle')}
                        </p>
                    </div>
                    <div className="flex items-center space-x-3">
                        <ExportButton
                            onExport={handleExport}
                            filteredCount={filteredOrders.length}
                            totalCount={orders.length}
                            disabled={isExporting || isLoading}
                            entityName="orders"
                        />
                    </div>
                </div>

                {/* Filters */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        {t('admin.orders.filters.title')}
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        {/* Date From */}
                        <div>
                            <DatePicker
                                label={t('admin.orders.filters.dateFrom')}
                                value={filters.dateFrom}
                                onChange={(value) => handleDateRangeChange('dateFrom', value)}
                                placeholder="dd/MM/yyyy"
                            />
                        </div>

                        {/* Date To */}
                        <div>
                            <DatePicker
                                label={t('admin.orders.filters.dateTo')}
                                value={filters.dateTo}
                                onChange={(value) => handleDateRangeChange('dateTo', value)}
                                placeholder="dd/MM/yyyy"
                            />
                        </div>

                        {/* Status Filter */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.filters.status')}
                            </label>
                            <select
                                value={filters.status}
                                onChange={(e) => handleStatusChange(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                <option value="">{t('admin.orders.filters.allStatuses')}</option>
                                {ORDER_STATUSES.map(status => (
                                    <option key={status} value={status}>
                                        {status}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Facility Filter */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.filters.facility')}
                            </label>
                            <select
                                value={filters.facilityKey}
                                onChange={(e) => handleFacilityChange(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                disabled={facilityKeys.length === 0}
                            >
                                <option value="">{t('admin.orders.filters.allFacilities')}</option>
                                {facilityKeys.map(facility => (
                                    <option key={facility} value={facility}>
                                        {facility}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>

                    {/* Filter Summary & Actions */}
                    <div className="mt-4 flex items-center justify-between text-sm text-gray-600">
                        <div>
                            {t('admin.orders.filters.showing', {
                                count: filteredOrders.length,
                                total: orders.length
                            })}
                        </div>
                        <div className="flex items-center space-x-3">
                            {/* Refresh Button */}
                            <button
                                onClick={handleRefresh}
                                disabled={isFetching}
                                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                title={isFetching ? t('admin.orders.filters.refreshing') : t('admin.orders.filters.refresh')}
                            >
                                <ArrowPathIcon className={`w-4 h-4 mr-2 ${isFetching ? 'animate-spin' : ''}`} />
                                {isFetching ? t('admin.orders.filters.refreshing') : t('admin.orders.filters.refresh')}
                            </button>

                            {/* Clear Filters Button */}
                            {(filters.status || filters.facilityKey) && (
                                <button
                                    onClick={clearStatusAndFacility}
                                    className="text-blue-600 hover:text-blue-800 font-medium"
                                >
                                    {t('admin.orders.filters.clearFilters')}
                                </button>
                            )}
                        </div>
                    </div>
                </div>

                {/* Orders Table */}
                <DataTable
                    data={filteredOrders}
                    columns={columns}
                    loading={isLoading}
                    emptyMessage={t('admin.orders.table.empty')}
                    onRowClick={handleRowClick}
                    getItemId={(order) => order.id}
                />
            </div>
        </AdminGuard>
    );
};

export default OrdersPage;