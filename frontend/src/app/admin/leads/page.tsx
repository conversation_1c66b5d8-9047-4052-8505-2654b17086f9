"use client";

import React, { useMemo, useState, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { AdminGuard } from '../../components/common/AdminGuard';
import DataTable, { Column } from '../components/DataTable';
import StatusBadge from '../components/StatusBadge';
import { getLeadsForAdmin } from '../../services/leadService';
import { Lead, LEAD_STATUSES, LEAD_SOURCES, LEAD_TYPES } from '../../types/lead';
import { useLeadFilters } from '../../hooks/useLeadFilters';

import { useDebounce } from '../../hooks/useDebounce';
import { maskMobile } from '@/lib/utils';
import { formatLeadMobile, formatLeadTimestamp } from '../../services/leadUtils';
import { useTranslation } from 'react-i18next';
import { logger } from '@/lib/logger';
// import DatePicker from '../../components/common/DatePicker';
import { ArrowPathIcon, MagnifyingGlassIcon, FunnelIcon, PhoneIcon, UserIcon } from '@heroicons/react/24/outline';

const LeadsPage: React.FC = () => {
    const { t } = useTranslation();
    const router = useRouter();
    const { filters, updateFilter, resetFilters, updatePage, currentPage, limit, getServerFilters, getClientFilters } = useLeadFilters();

    const [showFilters, setShowFilters] = useState(false);
    const [isExporting, setIsExporting] = useState(false);

    // Debounce search input for better performance
    const debouncedSearch = useDebounce(filters.search, 300);

    // Get server and client filters
    const serverFilters = getServerFilters();
    const clientFilters = getClientFilters();

    // Only include mobile search in query key to prevent unnecessary API calls for name searches
    const mobileSearchTerm = clientFilters.isMobileSearch ? debouncedSearch : '';

    // Fetch leads with pagination
    const {
        data: leadResponse,
        isLoading,
        isError,
        error,
        refetch,
        isFetching
    } = useQuery({
        queryKey: ['admin-leads', currentPage, limit, serverFilters, mobileSearchTerm],
        queryFn: () => getLeadsForAdmin(
            currentPage,
            limit,
            serverFilters
        ),
        staleTime: 30000, // 30 seconds
        gcTime: 300000, // 5 minutes
        refetchOnMount: true,
        refetchOnWindowFocus: false,
    });

    // Apply local filtering for name search (case-insensitive)
    const filteredLeads = useMemo(() => {
        const allLeads = leadResponse?.leads || [];

        if (!clientFilters.isNameSearch) {
            return allLeads;
        }

        const searchTerm = clientFilters.search.toLowerCase();
        return allLeads.filter(lead =>
            lead.name.toLowerCase().includes(searchTerm)
        );
    }, [leadResponse?.leads, clientFilters.isNameSearch, clientFilters.search]);

    const leads = filteredLeads;
    const totalLeads = leadResponse?.pagination?.totalRows || 0;
    const totalPages = Math.ceil(totalLeads / limit);

    // Handle search
    const handleSearch = (term: string) => {
        updateFilter('search', term);
    };

    // Handle filter changes
    const handleFilterChange = (key: string, value: string) => {
        updateFilter(key as any, value === 'all' ? '' : value);
    };

    // Handle page changes
    const handlePageChange = (page: number) => {
        updatePage(page);
    };

    // Handle refresh
    const handleRefresh = () => {
        logger.debug('Leads: Manual refresh triggered', { filters });
        refetch();
    };

    // Handle row click - navigate to lead details with filter preservation
    const handleRowClick = (lead: Lead) => {
        logger.debug('Lead clicked', { leadId: lead.id, currentFilters: filters });

        // Preserve current filters in URL for return navigation
        const currentParams = new URLSearchParams();
        if (filters.search) currentParams.set('search', filters.search);
        if (filters.status) currentParams.set('status', filters.status);
        if (filters.leadSource) currentParams.set('leadSource', filters.leadSource);
        if (filters.type) currentParams.set('type', filters.type);
        if (filters.dateFrom) currentParams.set('dateFrom', filters.dateFrom);
        if (filters.dateTo) currentParams.set('dateTo', filters.dateTo);
        if (currentPage > 1) currentParams.set('page', currentPage.toString());
        if (limit !== 10) currentParams.set('limit', limit.toString());

        const returnFilters = currentParams.toString();
        const detailUrl = returnFilters
            ? `/admin/leads/${lead.id}?returnFilters=${encodeURIComponent(returnFilters)}`
            : `/admin/leads/${lead.id}`;

        logger.debug('Navigating to lead detail with preserved filters', {
            leadId: lead.id,
            returnFilters,
            detailUrl
        });

        router.push(detailUrl);
    };

    // Handle export functionality
    const handleExport = async () => {
        if (leads.length === 0) {
            return;
        }

        setIsExporting(true);
        try {
            logger.debug('Leads export triggered', {
                leadsCount: leads.length,
                totalLeads,
                filters
            });

            // Format leads for export
            const exportData = leads.map(lead => ({
                id: lead.id,
                name: lead.name,
                mobile: lead.endCustomerMobile || '',
                type: lead.type,
                status: lead.status,
                source: lead.leadSource,
                createdAt: formatLeadTimestamp(lead.row_created_at),
                updatedAt: formatLeadTimestamp(lead.row_updated_at),
                conversionTime: lead.conversionTime ? formatLeadTimestamp(lead.conversionTime) : '',
                metadata: lead.metadata ? JSON.stringify(lead.metadata) : ''
            }));

            // Generate CSV
            const headers = [
                'ID', 'Name', 'Mobile', 'Type', 'Status', 'Source',
                'Created At', 'Updated At', 'Conversion Time', 'Metadata'
            ];

            const csvContent = [
                headers.join(','),
                ...exportData.map(row =>
                    Object.values(row).map(value =>
                        typeof value === 'string' && value.includes(',')
                            ? `"${value.replace(/"/g, '""')}"`
                            : value
                    ).join(',')
                )
            ].join('\n');

            // Create and download file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);

            const dateStr = new Date().toISOString().split('T')[0];
            let filename = `leads_${dateStr}`;
            if (filters.search) filename += `_search_${filters.search.replace(/[^a-zA-Z0-9]/g, '_')}`;
            if (filters.status) filename += `_${filters.status}`;
            filename += '.csv';

            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            logger.debug('Leads export completed', { filename, recordCount: exportData.length });
        } catch (error) {
            logger.error('Leads export failed', { error });
        } finally {
            setIsExporting(false);
        }
    };

    // Define table columns
    const columns: Column<Lead>[] = useMemo(() => [
        {
            key: 'id',
            header: t('leads.common.id'),
            width: '80px',
            render: (lead) => `#${lead.id}`,
            sortable: true,
            getSortValue: (lead) => lead.id
        },
        {
            key: 'name',
            header: t('leads.table.name'),
            width: '180px',
            render: (lead) => (
                <div>
                    <div className="font-medium text-gray-900">
                        {lead.name}
                    </div>
                    {lead.endCustomerMobile && (
                        <div className="text-sm text-gray-500">
                            {formatLeadMobile(lead.endCustomerMobile)}
                        </div>
                    )}
                </div>
            ),
            sortable: true,
            getSortValue: (lead) => lead.name
        },
        {
            key: 'type',
            header: t('leads.table.type'),
            width: '140px',
            render: (lead) => (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {t(`leads.types.${lead.type}`)}
                </span>
            ),
            sortable: true,
            getSortValue: (lead) => lead.type
        },
        {
            key: 'status',
            header: t('leads.table.status'),
            width: '120px',
            render: (lead) => <StatusBadge status={lead.status.toLowerCase()} />,
            sortable: true,
            getSortValue: (lead) => lead.status
        },
        {
            key: 'leadSource',
            header: t('leads.table.source'),
            width: '100px',
            render: (lead) => (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {t(`leads.sources.${lead.leadSource}`)}
                </span>
            ),
            sortable: true,
            getSortValue: (lead) => lead.leadSource
        },
        {
            key: 'row_created_at',
            header: t('leads.table.created'),
            width: '140px',
            render: (lead) => formatLeadTimestamp(lead.row_created_at),
            sortable: true,
            getSortValue: (lead) => lead.row_created_at
        },
        {
            key: 'row_updated_at',
            header: t('leads.table.updated'),
            width: '140px',
            render: (lead) => formatLeadTimestamp(lead.row_updated_at),
            sortable: true,
            getSortValue: (lead) => lead.row_updated_at
        }
    ], [t]);

    if (isError) {
        return (
            <AdminGuard requiredPermission="viewLeads">
                <div className="p-6">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 className="text-lg font-medium text-red-800 mb-2">
                            {t('leads.error')}
                        </h3>
                        <p className="text-red-600">
                            {error?.message || t('leads.common.unexpectedError')}
                        </p>
                        <button
                            onClick={() => refetch()}
                            className="mt-3 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                        >
                            {t('leads.common.retry')}
                        </button>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    return (
        <AdminGuard requiredPermission="viewLeads">
            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                            {t('leads.title')}
                        </h1>
                        <p className="text-gray-600 mt-1">
                            {totalLeads > 0
                                ? `${totalLeads} ${totalLeads === 1 ? t('leads.common.lead') : t('leads.common.leads')} ${t('leads.common.found')}`
                                : t('leads.common.subtitle')
                            }
                        </p>
                    </div>
                    <div className="flex items-center space-x-3">
                        <button
                            onClick={handleExport}
                            disabled={isExporting || isLoading || leads.length === 0}
                            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                            {isExporting ? t('leads.common.exporting') : t('leads.exportLeads')}
                        </button>

                        <button
                            onClick={handleRefresh}
                            disabled={isLoading || isFetching}
                            className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                            title={t('leads.refreshLeads')}
                        >
                            <ArrowPathIcon className={`h-5 w-5 ${(isLoading || isFetching) ? 'animate-spin' : ''}`} />
                        </button>
                    </div>
                </div>

                {/* Search and Filters Bar */}
                <div className="bg-white p-4 rounded-lg shadow">
                    <div className="flex items-center space-x-4 mb-4">
                        {/* Search with Visual Indicators */}
                        <div className="flex-1 relative">
                            {/* Dynamic Icon based on search type */}
                            {clientFilters.isMobileSearch ? (
                                <PhoneIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-blue-500" />
                            ) : clientFilters.isNameSearch ? (
                                <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-green-500" />
                            ) : (
                                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                            )}

                            <input
                                type="text"
                                placeholder={t('leads.searchPlaceholder')}
                                value={filters.search}
                                onChange={(e) => handleSearch(e.target.value)}
                                className={`w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:border-transparent ${clientFilters.isMobileSearch
                                    ? 'border-blue-300 focus:ring-blue-500'
                                    : clientFilters.isNameSearch
                                        ? 'border-green-300 focus:ring-green-500'
                                        : 'border-gray-300 focus:ring-blue-500'
                                    }`}
                            />

                            {/* Search Type Indicator */}
                            {(clientFilters.isMobileSearch || clientFilters.isNameSearch) && (
                                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                    <span className={`text-xs px-2 py-1 rounded-full ${clientFilters.isMobileSearch
                                        ? 'bg-blue-100 text-blue-700'
                                        : 'bg-green-100 text-green-700'
                                        }`}>
                                        {clientFilters.isMobileSearch ? t('leads.search.mobile') : t('leads.search.name')}
                                    </span>
                                </div>
                            )}
                        </div>

                        {/* Filter Toggle */}
                        <button
                            onClick={() => setShowFilters(!showFilters)}
                            className={`flex items-center px-3 py-2 border rounded-md transition-colors ${showFilters
                                ? 'bg-blue-50 border-blue-300 text-blue-700'
                                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                                }`}
                        >
                            <FunnelIcon className="h-4 w-4 mr-2" />
                            {t('leads.common.filters')}
                        </button>

                        {/* Clear Filters */}
                        {(filters.status || filters.leadSource || filters.type || filters.dateFrom || filters.dateTo) && (
                            <button
                                onClick={resetFilters}
                                className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"
                            >
                                {t('leads.filters.clearFilters')}
                            </button>
                        )}
                    </div>

                    {/* Expandable Filters */}
                    {showFilters && (
                        <div className="border-t pt-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {/* Status Filter */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        {t('leads.filters.status')}
                                    </label>
                                    <select
                                        value={filters.status}
                                        onChange={(e) => handleFilterChange('status', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">{t('leads.filters.all')}</option>
                                        {LEAD_STATUSES.map(status => (
                                            <option key={status} value={status}>
                                                {t(`leads.statuses.${status}`)}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                {/* Source Filter */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        {t('leads.filters.source')}
                                    </label>
                                    <select
                                        value={filters.leadSource}
                                        onChange={(e) => handleFilterChange('leadSource', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">{t('leads.filters.all')}</option>
                                        {LEAD_SOURCES.map(source => (
                                            <option key={source} value={source}>
                                                {t(`leads.sources.${source}`)}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                {/* Type Filter */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        {t('leads.filters.type')}
                                    </label>
                                    <select
                                        value={filters.type}
                                        onChange={(e) => handleFilterChange('type', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">{t('leads.filters.all')}</option>
                                        {LEAD_TYPES.map(type => (
                                            <option key={type} value={type}>
                                                {t(`leads.types.${type}`)}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                {/* Date From - Commented out for now */}
                                {/* <div>
                                    <DatePicker
                                        label={t('leads.filters.dateFrom')}
                                        value={filters.dateFrom}
                                        onChange={(value) => handleFilterChange('dateFrom', value)}
                                        placeholder="dd/MM/yyyy"
                                    />
                                </div> */}

                                {/* Date To - Commented out for now */}
                                {/* <div>
                                    <DatePicker
                                        label={t('leads.filters.dateTo')}
                                        value={filters.dateTo}
                                        onChange={(value) => handleFilterChange('dateTo', value)}
                                        placeholder="dd/MM/yyyy"
                                    />
                                </div> */}
                            </div>
                        </div>
                    )}
                </div>

                {/* Leads Table */}
                <DataTable
                    data={leads}
                    columns={columns}
                    loading={isLoading}
                    emptyMessage={t('leads.noLeads')}
                    onRowClick={handleRowClick}
                    getItemId={(lead) => lead.id.toString()}
                />

                {/* Pagination */}
                {totalPages > 1 && (
                    <div className="bg-white px-4 py-3 flex items-center justify-between border border-gray-200 rounded-lg shadow">
                        <div className="flex-1 flex justify-between sm:hidden">
                            <button
                                onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                                disabled={currentPage <= 1}
                                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {t('leads.pagination.previous')}
                            </button>
                            <button
                                onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                                disabled={currentPage >= totalPages}
                                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {t('leads.pagination.next')}
                            </button>
                        </div>
                        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p className="text-sm text-gray-700">
                                    {t('leads.pagination.showing')}{' '}
                                    <span className="font-medium">{((currentPage - 1) * limit) + 1}</span>
                                    {' '}{t('leads.pagination.to')}{' '}
                                    <span className="font-medium">
                                        {Math.min(currentPage * limit, totalLeads)}
                                    </span>
                                    {' '}{t('leads.pagination.of')}{' '}
                                    <span className="font-medium">{totalLeads}</span>
                                    {' '}{t('leads.pagination.results')}
                                </p>
                            </div>
                            <div>
                                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <button
                                        onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                                        disabled={currentPage <= 1}
                                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                                    >
                                        {t('leads.pagination.previous')}
                                    </button>

                                    {/* Page Numbers */}
                                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                        let pageNum;
                                        if (totalPages <= 5) {
                                            pageNum = i + 1;
                                        } else if (currentPage <= 3) {
                                            pageNum = i + 1;
                                        } else if (currentPage >= totalPages - 2) {
                                            pageNum = totalPages - 4 + i;
                                        } else {
                                            pageNum = currentPage - 2 + i;
                                        }
                                        return (
                                            <button
                                                key={pageNum}
                                                onClick={() => handlePageChange(pageNum)}
                                                className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium cursor-pointer ${pageNum === currentPage
                                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                                    }`}
                                            >
                                                {pageNum}
                                            </button>
                                        );
                                    })}

                                    <button
                                        onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                                        disabled={currentPage >= totalPages}
                                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                                    >
                                        {t('leads.pagination.next')}
                                    </button>
                                </nav>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </AdminGuard>
    );
};

export default LeadsPage; 