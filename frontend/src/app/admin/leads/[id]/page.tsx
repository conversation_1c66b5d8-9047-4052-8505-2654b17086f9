"use client";

import React, { useState, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter, useSearchParams, useParams } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import Modal from 'react-modal';
import { useAuth } from '../../../context/AuthContext';
import { AdminGuard } from '../../../components/common/AdminGuard';
import StatusBadge from '../../components/StatusBadge';
import DatePicker from '../../../components/common/DatePicker';
import { getLeadById, updateLead } from '../../../services/leadService';
import { Lead, LEAD_STATUSES, LEAD_SOURCES, LEAD_TYPES, LeadMetadata, LeadNote, LeadTimelineEntry } from '../../../types/lead';
import { formatLeadMobile, formatLeadTimestamp } from '../../../services/leadUtils';

import { logger } from '@/lib/logger';
import {
    ArrowLeftIcon,
    PencilIcon,
    CheckIcon,
    XMarkIcon,
    PlusIcon,
    ClockIcon,
    UserIcon,
    PhoneIcon,
    TagIcon,
    CalendarIcon,
    DocumentTextIcon,
    ChartBarIcon
} from '@heroicons/react/24/outline';

// Modal component for status updates using react-modal
interface StatusUpdateModalProps {
    isOpen: boolean;
    onClose: () => void;
    currentStatus: string;
    onUpdate: (newStatus: string, reason?: string) => void;
    isLoading: boolean;
}

const StatusUpdateModal: React.FC<StatusUpdateModalProps> = ({
    isOpen,
    onClose,
    currentStatus,
    onUpdate,
    isLoading
}) => {
    const { t } = useTranslation();
    const [newStatus, setNewStatus] = useState(currentStatus);
    const [reason, setReason] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onUpdate(newStatus, reason.trim() || undefined);
    };

    const modalStyles = {
        content: {
            top: '50%',
            left: '50%',
            right: 'auto',
            bottom: 'auto',
            marginRight: '-50%',
            transform: 'translate(-50%, -50%)',
            maxWidth: '500px',
            width: '90%',
            padding: '24px',
            borderRadius: '8px',
            border: 'none',
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
        },
        overlay: {
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1000
        }
    };

    return (
        <Modal
            isOpen={isOpen}
            onRequestClose={onClose}
            style={modalStyles}
            contentLabel={t('leads.detail.statusUpdate.title')}
            ariaHideApp={false}
        >
            <h3 className="text-lg font-medium text-gray-900 mb-4">
                {t('leads.detail.statusUpdate.title')}
            </h3>

            <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('leads.detail.statusUpdate.currentStatus')}
                    </label>
                    <div className="flex items-center">
                        <StatusBadge status={currentStatus.toLowerCase()} />
                    </div>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('leads.detail.statusUpdate.newStatus')}
                    </label>
                    <select
                        value={newStatus}
                        onChange={(e) => setNewStatus(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                    >
                        {LEAD_STATUSES.map(status => (
                            <option key={status} value={status}>
                                {status}
                            </option>
                        ))}
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('leads.detail.statusUpdate.reason')}
                    </label>
                    <textarea
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        placeholder={t('leads.detail.statusUpdate.reasonPlaceholder')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                        rows={3}
                    />
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                    <button
                        type="button"
                        onClick={onClose}
                        disabled={isLoading}
                        className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                    >
                        {t('leads.detail.statusUpdate.cancel')}
                    </button>
                    <button
                        type="submit"
                        disabled={isLoading || newStatus === currentStatus}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                    >
                        {isLoading ? '...' : t('leads.detail.statusUpdate.update')}
                    </button>
                </div>
            </form>
        </Modal>
    );
};

// Notes component
interface NotesComponentProps {
    notes: LeadNote[];
    onAddNote: (content: string) => void;
    isLoading: boolean;
    isEditing: boolean;
}

const NotesComponent: React.FC<NotesComponentProps> = ({ notes, onAddNote, isLoading, isEditing }) => {
    const { t } = useTranslation();
    const { userDetails } = useAuth();
    const [isAdding, setIsAdding] = useState(false);
    const [newNote, setNewNote] = useState('');

    const getUserName = (userId: number): string => {
        if (userDetails && userDetails.id === userId) {
            return userDetails.name;
        }
        return `User ${userId}`;
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (newNote.trim()) {
            onAddNote(newNote.trim());
            setNewNote('');
            setIsAdding(false);
        }
    };

    return (
        <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                    <DocumentTextIcon className="h-5 w-5 mr-2 text-gray-500" />
                    {t('leads.detail.notes.title')}
                </h3>
                {!isAdding && isEditing && (
                    <button
                        onClick={() => setIsAdding(true)}
                        className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                        <PlusIcon className="h-4 w-4 inline mr-1" />
                        {t('leads.detail.notes.addNote')}
                    </button>
                )}
            </div>

            {isAdding && (
                <form onSubmit={handleSubmit} className="mb-4 p-4 border border-gray-200 rounded-md">
                    <textarea
                        value={newNote}
                        onChange={(e) => setNewNote(e.target.value)}
                        placeholder={t('leads.detail.notes.notePlaceholder')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                        rows={3}
                        required
                        autoFocus
                    />
                    <div className="flex justify-end space-x-2 mt-3">
                        <button
                            type="button"
                            onClick={() => {
                                setIsAdding(false);
                                setNewNote('');
                            }}
                            disabled={isLoading}
                            className="px-3 py-1 text-sm text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                        >
                            {t('leads.detail.notes.cancel')}
                        </button>
                        <button
                            type="submit"
                            disabled={isLoading || !newNote.trim()}
                            className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                        >
                            {isLoading ? '...' : t('leads.detail.notes.save')}
                        </button>
                    </div>
                </form>
            )}

            <div className="space-y-3">
                {notes && notes.length > 0 ? (
                    notes.map((note) => (
                        <div key={note.id} className="p-3 bg-gray-50 rounded-md">
                            <p className="text-gray-900 text-sm">{note.content}</p>
                            <p className="text-xs text-gray-500 mt-2">
                                {formatLeadTimestamp(note.createdAt)} • {getUserName(note.createdBy)}
                            </p>
                        </div>
                    ))
                ) : (
                    <p className="text-gray-500 text-sm italic">{t('leads.detail.notes.noNotes')}</p>
                )}
            </div>
        </div>
    );
};

// Timeline component
interface TimelineComponentProps {
    timeline: LeadTimelineEntry[];
}

const TimelineComponent: React.FC<TimelineComponentProps> = ({ timeline }) => {
    const { t } = useTranslation();
    const { userDetails } = useAuth();

    // Sort timeline in reverse chronological order (newest first)
    const sortedTimeline = timeline ? [...timeline].sort((a, b) => b.timestamp - a.timestamp) : [];

    const getUserName = (userId: number): string => {
        if (userDetails && userDetails.id === userId) {
            return userDetails.name;
        }
        return `User ${userId}`;
    };

    return (
        <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <ClockIcon className="h-5 w-5 mr-2 text-gray-500" />
                {t('leads.detail.timeline.title')}
            </h3>

            <div className="space-y-4">
                {sortedTimeline.length > 0 ? (
                    sortedTimeline.map((entry) => (
                        <div key={entry.id} className="flex items-start space-x-3">
                            <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                            <div className="flex-1">
                                <p className="text-sm font-medium text-gray-900">{entry.action}</p>
                                <p className="text-sm text-gray-600">{entry.details}</p>
                                <p className="text-xs text-gray-500 mt-1">
                                    {formatLeadTimestamp(entry.timestamp)} • {getUserName(entry.userId)}
                                </p>
                            </div>
                        </div>
                    ))
                ) : (
                    <p className="text-gray-500 text-sm italic">{t('leads.detail.timeline.noTimeline')}</p>
                )}
            </div>
        </div>
    );
};

const LeadDetailPage: React.FC = () => {
    const { t } = useTranslation();
    const router = useRouter();
    const params = useParams();
    const searchParams = useSearchParams();
    const queryClient = useQueryClient();
    const { userDetails } = useAuth();
    const leadId = parseInt(params.id as string, 10);

    const [isEditing, setIsEditing] = useState(false);
    const [editData, setEditData] = useState<Partial<Lead>>({});
    const [showStatusModal, setShowStatusModal] = useState(false);

    // Fetch lead data
    const {
        data: lead,
        isLoading,
        isError,
        error,
        refetch
    } = useQuery<Lead, Error>({
        queryKey: ['admin-lead', leadId],
        queryFn: () => getLeadById(leadId),
        enabled: !isNaN(leadId),
        staleTime: 30000,
        gcTime: 300000,
    });

    // Update lead mutation
    const updateLeadMutation = useMutation({
        mutationFn: async (updateData: Partial<Lead>) => {
            logger.debug('Lead update requested', { leadId, updateData });
            return await updateLead(leadId, { ...lead, ...updateData });
        },
        onSuccess: (updatedLead) => {
            queryClient.setQueryData(['admin-lead', leadId], updatedLead);
            queryClient.invalidateQueries({ queryKey: ['admin-leads'] });
            setIsEditing(false);
            setEditData({});
            logger.debug('Lead updated successfully', { leadId });
            // toast.success(t('leads.detail.updateSuccess'));
        },
        onError: (error: Error) => {
            logger.error('Failed to update lead', error);
            // toast.error(error.message || t('leads.detail.updateError'));
        }
    });

    // Status update - local state only, no backend call
    const handleStatusUpdate = useCallback((newStatus: string, reason?: string) => {
        logger.debug('Lead status update requested', { leadId, status: newStatus, reason });

        // Create timeline entry for status change
        const timelineEntry: LeadTimelineEntry = {
            id: Date.now().toString(),
            action: t('leads.detail.timeline.statusChanged'),
            details: `${lead?.status} → ${newStatus}${reason ? ` (${reason})` : ''}`,
            timestamp: Date.now(),
            userId: userDetails?.id || 0
        };

        // Update local editData state
        setEditData(prev => ({
            ...prev,
            status: newStatus as Lead['status'],
            metadata: {
                ...prev.metadata,
                timeline: [...(prev.metadata?.timeline || lead?.metadata?.timeline || []), timelineEntry],
                // If status is LOST and reason provided, update lostReason
                ...(newStatus === 'LOST' && reason ? { lostReason: reason } : {})
            }
        }));

        setShowStatusModal(false);
    }, [leadId, lead?.status, lead?.metadata, userDetails?.id, t]);

    // Add note - local state only, no backend call
    const handleAddNote = useCallback((content: string) => {
        logger.debug('Add note requested', { leadId, content });

        const newNote: LeadNote = {
            id: Date.now().toString(),
            content,
            createdAt: Date.now(),
            createdBy: userDetails?.id || 0
        };

        const timelineEntry: LeadTimelineEntry = {
            id: (Date.now() + 1).toString(),
            action: t('leads.detail.timeline.noteAdded'),
            details: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
            timestamp: Date.now(),
            userId: userDetails?.id || 0
        };

        // Update local editData state
        setEditData(prev => ({
            ...prev,
            metadata: {
                ...prev.metadata,
                notes: [...(prev.metadata?.notes || lead?.metadata?.notes || []), newNote],
                timeline: [...(prev.metadata?.timeline || lead?.metadata?.timeline || []), timelineEntry]
            }
        }));
    }, [leadId, userDetails?.id, t, lead?.metadata]);

    const handleFieldChange = useCallback((field: keyof Lead, value: any) => {
        setEditData(prev => ({ ...prev, [field]: value }));
    }, []);

    const handleMetadataChange = useCallback((field: keyof LeadMetadata, value: any) => {
        setEditData(prev => ({
            ...prev,
            metadata: { ...prev.metadata, [field]: value }
        }));
    }, []);

    const handleSave = useCallback(() => {
        if (Object.keys(editData).length > 0) {
            updateLeadMutation.mutate(editData);
        } else {
            setIsEditing(false);
        }
    }, [editData, updateLeadMutation]);





    const currentData = useMemo(() => {
        return isEditing ? { ...lead, ...editData } : lead;
    }, [lead, editData, isEditing]);

    // Navigation with filter preservation
    const handleBack = useCallback(() => {
        const returnFilters = searchParams.get('returnFilters');
        if (returnFilters) {
            const decodedFilters = decodeURIComponent(returnFilters);
            logger.debug('Returning to leads with filters', { decodedFilters });
            router.push(`/admin/leads?${decodedFilters}`);
        } else {
            router.push('/admin/leads');
        }
    }, [router, searchParams]);

    if (isNaN(leadId)) {
        return (
            <AdminGuard requiredPermission="viewLeads">
                <div className="p-6">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 className="text-lg font-medium text-red-800">
                            {t('leads.detail.invalidId')}
                        </h3>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    if (isLoading) {
        return (
            <AdminGuard requiredPermission="viewLeads">
                <div className="p-6">
                    <div className="animate-pulse space-y-6">
                        <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                        <div className="bg-white p-6 rounded-lg shadow space-y-4">
                            <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                        </div>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    if (isError || !lead) {
        return (
            <AdminGuard requiredPermission="viewLeads">
                <div className="p-6">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 className="text-lg font-medium text-red-800 mb-2">
                            {t('leads.detail.notFound')}
                        </h3>
                        <p className="text-red-600 mb-4">
                            {error?.message || t('leads.detail.loadError')}
                        </p>
                        <div className="flex space-x-3">
                            <button
                                onClick={() => refetch()}
                                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                            >
                                {t('leads.common.retry')}
                            </button>
                            <button
                                onClick={handleBack}
                                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                            >
                                {t('leads.detail.backToLeads')}
                            </button>
                        </div>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    return (
        <AdminGuard requiredPermission="viewLeads">
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <button
                            onClick={handleBack}
                            className="text-gray-400 hover:text-gray-600"
                        >
                            <ArrowLeftIcon className="w-6 h-6" />
                        </button>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">
                                {t('leads.detail.title', { id: lead.id })}
                            </h1>
                            <p className="text-gray-600 mt-1">
                                {t('leads.detail.subtitle')}
                            </p>
                        </div>
                    </div>

                    <div className="flex items-center space-x-3">
                        {!isEditing ? (
                            <button
                                onClick={() => {
                                    // Initialize editData with current lead values when entering edit mode
                                    setEditData({
                                        name: lead?.name || '',
                                        status: lead?.status,
                                        metadata: {
                                            lostReason: lead?.metadata?.lostReason || '',
                                            leadScore: lead?.metadata?.leadScore || undefined,
                                            nextFollowUp: lead?.metadata?.nextFollowUp || undefined,
                                            conversionData: lead?.metadata?.conversionData || undefined,
                                            notes: lead?.metadata?.notes || [],
                                            timeline: lead?.metadata?.timeline || []
                                        }
                                    });
                                    setIsEditing(true);
                                }}
                                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                            >
                                <PencilIcon className="h-4 w-4 inline mr-2" />
                                {t('leads.detail.editLead')}
                            </button>
                        ) : (
                            <>
                                <button
                                    onClick={() => {
                                        setIsEditing(false);
                                        setEditData({});
                                    }}
                                    disabled={updateLeadMutation.isPending}
                                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50"
                                >
                                    <XMarkIcon className="h-4 w-4 inline mr-2" />
                                    {t('leads.detail.cancelEdit')}
                                </button>
                                <button
                                    onClick={handleSave}
                                    disabled={updateLeadMutation.isPending}
                                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
                                >
                                    <CheckIcon className="h-4 w-4 inline mr-2" />
                                    {updateLeadMutation.isPending ? '...' : t('leads.detail.saveLead')}
                                </button>
                            </>
                        )}
                    </div>
                </div>

                {/* Lead Overview */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <UserIcon className="h-5 w-5 mr-2 text-gray-500" />
                        {t('leads.detail.overview.title')}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('leads.detail.overview.status')}
                            </label>
                            <div className="flex items-center space-x-3">
                                <StatusBadge status={currentData?.status?.toLowerCase() || ''} />
                                {isEditing && (
                                    <button
                                        onClick={() => setShowStatusModal(true)}
                                        className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                                    >
                                        {t('leads.detail.actions.updateStatus')}
                                    </button>
                                )}
                            </div>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('leads.detail.overview.type')}
                            </label>
                            <div className="flex items-center">
                                <TagIcon className="h-4 w-4 mr-2 text-gray-400" />
                                <span className="text-gray-900">{currentData?.type}</span>
                            </div>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('leads.detail.overview.source')}
                            </label>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {currentData?.leadSource}
                            </span>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('leads.detail.overview.created')}
                            </label>
                            <div className="flex items-center">
                                <CalendarIcon className="h-4 w-4 mr-2 text-gray-400" />
                                <span className="text-gray-900">
                                    {formatLeadTimestamp(currentData?.row_created_at || 0)}
                                </span>
                            </div>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('leads.detail.overview.updated')}
                            </label>
                            <div className="flex items-center">
                                <CalendarIcon className="h-4 w-4 mr-2 text-gray-400" />
                                <span className="text-gray-900">
                                    {formatLeadTimestamp(currentData?.row_updated_at || 0)}
                                </span>
                            </div>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('leads.detail.overview.conversionTime')}
                            </label>
                            <span className="text-gray-900">
                                {currentData?.conversionTime
                                    ? formatLeadTimestamp(currentData.conversionTime)
                                    : t('leads.detail.overview.noConversion')
                                }
                            </span>
                        </div>
                    </div>
                </div>

                {/* Contact Information */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <PhoneIcon className="h-5 w-5 mr-2 text-gray-500" />
                        {t('leads.detail.contact.title')}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('leads.detail.contact.name')}
                            </label>
                            {isEditing ? (
                                <input
                                    type="text"
                                    value={editData.name || ''}
                                    onChange={(e) => handleFieldChange('name', e.target.value)}
                                    placeholder={t('leads.detail.contact.namePlaceholder')}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            ) : (
                                <p className="text-gray-900">{currentData?.name}</p>
                            )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('leads.detail.contact.mobile')}
                            </label>
                            <p className="text-gray-900">{formatLeadMobile(currentData?.endCustomerMobile || '')}</p>
                        </div>
                    </div>
                </div>

                {/* Notes */}
                <NotesComponent
                    notes={currentData?.metadata?.notes || []}
                    onAddNote={handleAddNote}
                    isLoading={false}
                    isEditing={isEditing}
                />

                {/* Timeline */}
                <TimelineComponent timeline={currentData?.metadata?.timeline || []} />

                {/* Additional Information */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <ChartBarIcon className="h-5 w-5 mr-2 text-gray-500" />
                        {t('leads.detail.metadata.title')}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Lost Reason - only show when status is LOST */}
                        {currentData?.status === 'LOST' && (
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('leads.detail.metadata.lostReason')}
                                </label>
                                <textarea
                                    value={currentData?.metadata?.lostReason || ''}
                                    readOnly
                                    placeholder={t('leads.detail.metadata.lostReasonPlaceholder')}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 cursor-not-allowed resize-none"
                                    rows={3}
                                />
                            </div>
                        )}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('leads.detail.metadata.leadScore')}
                            </label>
                            {isEditing ? (
                                <input
                                    type="number"
                                    min="1"
                                    max="10"
                                    value={editData.metadata?.leadScore || ''}
                                    onChange={(e) => handleMetadataChange('leadScore', parseInt(e.target.value) || undefined)}
                                    placeholder={t('leads.detail.metadata.leadScorePlaceholder')}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            ) : (
                                <p className="text-gray-900">
                                    {currentData?.metadata?.leadScore || t('leads.detail.metadata.noLeadScore')}
                                </p>
                            )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('leads.detail.metadata.nextFollowUp')}
                            </label>
                            {isEditing ? (
                                <DatePicker
                                    value={editData.metadata?.nextFollowUp
                                        ? new Date(editData.metadata.nextFollowUp).toISOString().split('T')[0]
                                        : ''
                                    }
                                    onChange={(dateString) => {
                                        const timestamp = dateString ? new Date(dateString).getTime() : undefined;
                                        handleMetadataChange('nextFollowUp', timestamp);
                                    }}
                                    placeholder="dd/MM/yyyy"
                                />
                            ) : (
                                <p className="text-gray-900">
                                    {currentData?.metadata?.nextFollowUp
                                        ? formatLeadTimestamp(currentData.metadata.nextFollowUp)
                                        : t('leads.detail.metadata.noNextFollowUp')
                                    }
                                </p>
                            )}
                        </div>
                    </div>
                </div>

                {/* Conversion Data */}
                {(currentData?.status === 'CONVERTED' || currentData?.metadata?.conversionData) && (
                    <div className="bg-white p-6 rounded-lg shadow">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            {t('leads.detail.conversion.title')}
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('leads.detail.conversion.orderId')}
                                </label>
                                {isEditing ? (
                                    <input
                                        type="text"
                                        value={editData.metadata?.conversionData?.orderId || currentData?.metadata?.conversionData?.orderId || ''}
                                        onChange={(e) => handleMetadataChange('conversionData', {
                                            ...currentData?.metadata?.conversionData,
                                            orderId: e.target.value
                                        })}
                                        placeholder={t('leads.detail.conversion.orderIdPlaceholder')}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                ) : (
                                    <p className="text-gray-900">
                                        {currentData?.metadata?.conversionData?.orderId || t('leads.detail.conversion.noConversionData')}
                                    </p>
                                )}
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('leads.detail.conversion.orderValue')}
                                </label>
                                {isEditing ? (
                                    <input
                                        type="number"
                                        step="0.01"
                                        value={editData.metadata?.conversionData?.orderValue || currentData?.metadata?.conversionData?.orderValue || ''}
                                        onChange={(e) => handleMetadataChange('conversionData', {
                                            ...currentData?.metadata?.conversionData,
                                            orderValue: parseFloat(e.target.value) || undefined
                                        })}
                                        placeholder={t('leads.detail.conversion.orderValuePlaceholder')}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                ) : (
                                    <p className="text-gray-900">
                                        {currentData?.metadata?.conversionData?.orderValue
                                            ? `₹${currentData.metadata.conversionData.orderValue.toFixed(2)}`
                                            : t('leads.detail.conversion.noConversionData')
                                        }
                                    </p>
                                )}
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('leads.detail.conversion.convertedAt')}
                                </label>
                                <p className="text-gray-900">
                                    {currentData?.metadata?.conversionData?.convertedAt
                                        ? formatLeadTimestamp(currentData.metadata.conversionData.convertedAt)
                                        : currentData?.conversionTime
                                            ? formatLeadTimestamp(currentData.conversionTime)
                                            : t('leads.detail.conversion.noConversionData')
                                    }
                                </p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Status Update Modal */}
                <StatusUpdateModal
                    isOpen={showStatusModal}
                    onClose={() => setShowStatusModal(false)}
                    currentStatus={lead.status}
                    onUpdate={handleStatusUpdate}
                    isLoading={false}
                />
            </div>
        </AdminGuard>
    );
};

export default LeadDetailPage; 