'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { getSkus } from '@/app/services/skuService';
import { SkuImportExportService } from '@/app/services/SkuImportExportService';
import { SkuOperationsService, BulkOperation } from '@/app/services/skuOperationsService';
import { getOrderedCategories } from '@/app/services/categoryService';
import { templateService } from '@/app/services/templateService';
import { excelService } from '@/app/services/excelService';
import ImportExportPanel from '@/app/admin/components/ImportExportPanel';
import type { SKU } from '@/app/types/sku';
import type { Category } from '@/app/types/category';
import {
    PlusIcon,
    FunnelIcon,
    MagnifyingGlassIcon,
    EyeIcon,
    PencilIcon,
    ChevronDownIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import Image from 'next/image';
import { AdminGuard } from '@/app/components/common/AdminGuard';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import React from 'react';
import { addNavigationParams } from '@/lib/utils';

// Custom hook for debouncing
function useDebounce<T>(value: T, delay: number): T {
    const [debouncedValue, setDebouncedValue] = useState<T>(value);

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => {
            clearTimeout(handler);
        };
    }, [value, delay]);

    return debouncedValue;
}

// SKU status options
const SKU_STATUSES = [
    { value: 'active', label: 'Active', color: 'green' },
    { value: 'inactive', label: 'Inactive', color: 'gray' }
] as const;

interface DisplaySKU extends SKU {
    depth: number;
    isChildDisplay: boolean; // Indicates if it's a child in the display hierarchy
    // children property is implicitly added by the function if they exist in skusToDisplay
}

const prepareHierarchicalSkus = (skusToDisplay: SKU[]): DisplaySKU[] => {
    const displaySkus: DisplaySKU[] = [];
    const skuMap = new Map(skusToDisplay.map(sku => [sku.skuId, { ...sku, _children: [] as DisplaySKU[] }]));

    // Populate children arrays
    skuMap.forEach(skuNode => {
        if (skuNode.parentId && skuMap.has(skuNode.parentId)) {
            const parentNode = skuMap.get(skuNode.parentId);
            parentNode?._children.push(skuNode as unknown as DisplaySKU); // Cast needed due to intermediate _children
        }
    });

    // Recursive function to flatten the hierarchy
    const addWithChildren = (skuNode: ReturnType<typeof skuMap.get>, depth: number) => {
        if (!skuNode) return;

        displaySkus.push({
            ...skuNode,
            depth: depth,
            isChildDisplay: depth > 0,
            variants: skuNode._children.map(c => ({ // Rebuild variants for the DisplaySKU based on _children
                skuId: c.skuId,
                name: c.name,
                costPrice: c.costPrice,
                sellingPrice: c.sellingPrice,
                mrp: c.mrp,
                type: 'child',
                isActive: c.isActive ?? 1, // Include child's active status
                variantName: c.variantName, // CRITICAL: Preserve variantName for display
                // Ensure other necessary variant fields are mapped if needed by table/actions
            })),
        } as DisplaySKU);

        skuNode._children.forEach(childNode => {
            addWithChildren(childNode as unknown as ReturnType<typeof skuMap.get>, depth + 1); // Cast needed
        });
    };

    // Add root SKUs (those without a parent in the current filtered list)
    skuMap.forEach(skuNode => {
        if (!skuNode.parentId || !skuMap.has(skuNode.parentId)) {
            addWithChildren(skuNode, 0);
        }
    });

    return displaySkus;
};

interface Filters {
    search: string;
    category: string;
    status: string;
    priceMin: string;
    priceMax: string;
}

export default function SkusPage() {
    const { t } = useTranslation();
    // State management
    const [skus, setSkus] = useState<SKU[]>([]);
    const [categories, setCategories] = useState<Category[]>([]);
    const [selectedSkus, setSelectedSkus] = useState<SKU[]>([]);
    const [modifiedSkus, setModifiedSkus] = useState<SKU[]>([]);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [showFilters, setShowFilters] = useState(false);
    const [filters, setFilters] = useState<Filters>({
        search: '',
        category: '',
        status: '',
        priceMin: '',
        priceMax: ''
    });

    // Import/Export state
    const [hasImportedChanges, setHasImportedChanges] = useState(false);
    const [importedSkus, setImportedSkus] = useState<SKU[]>([]);
    const [isSaving, setIsSaving] = useState(false);

    // Debounce search input for better performance
    const debouncedSearch = useDebounce(filters.search, 300);

    // Determine which SKUs to use as base data
    const currentDisplaySkus = hasImportedChanges ? importedSkus : modifiedSkus;

    // Memoized category lookup map for performance
    const categoryLookupMap = useMemo(() => {
        const lookupMap = new Map<number, Category[]>();
        categories.forEach(category => {
            category.skuIds.forEach(skuId => {
                if (!lookupMap.has(skuId)) {
                    lookupMap.set(skuId, []);
                }
                lookupMap.get(skuId)!.push(category);
            });
        });
        return lookupMap;
    }, [categories]);

    // Optimized category getter using memoized lookup
    const getSkuCategories = useCallback((skuId: number): Category[] => {
        return categoryLookupMap.get(skuId) || [];
    }, [categoryLookupMap]);

    // Memoized filtered SKUs with performance optimization
    const filteredSkus = useMemo(() => {
        let filtered = [...currentDisplaySkus];

        // Search filter (using debounced value)
        if (debouncedSearch) {
            const searchLower = debouncedSearch.toLowerCase();
            filtered = filtered.filter(sku =>
                sku.name.en.toLowerCase().includes(searchLower) ||
                (sku.name.ta && sku.name.ta.toLowerCase().includes(searchLower)) ||
                sku.skuId.toString().includes(searchLower)
            );
        }

        // Category filter
        if (filters.category) {
            const categoryId = parseInt(filters.category);
            const category = categories.find(cat => cat.id === categoryId);
            if (category) {
                filtered = filtered.filter(sku =>
                    category.skuIds.includes(sku.skuId)
                );
            }
        }

        // Status filter
        if (filters.status) {
            const statusValue = filters.status === 'active' ? 1 : 0;
            filtered = filtered.filter(sku => sku.isActive === statusValue);
        }

        // Price range filter
        if (filters.priceMin) {
            const minPrice = parseFloat(filters.priceMin);
            if (!isNaN(minPrice)) {
                filtered = filtered.filter(sku => sku.sellingPrice! >= minPrice);
            }
        }
        if (filters.priceMax) {
            const maxPrice = parseFloat(filters.priceMax);
            if (!isNaN(maxPrice)) {
                filtered = filtered.filter(sku => sku.sellingPrice! <= maxPrice);
            }
        }

        return filtered;
    }, [currentDisplaySkus, debouncedSearch, filters.category, filters.status, filters.priceMin, filters.priceMax, categories]);

    // Memoized hierarchical SKUs for display (FIXED: now uses filteredSkus instead of currentDisplaySkus)
    const skusForDisplay = useMemo(() => {
        return prepareHierarchicalSkus(filteredSkus);
    }, [filteredSkus]);

    // Load data
    const loadData = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);

            // Fetch SKUs and categories in parallel
            const [skusList, categoriesList] = await Promise.all([
                getSkus({ forceRefresh: true, allowInactive: true }), // Admin can see inactive SKUs
                getOrderedCategories({ includeInactive: true }) // Direct service call for admin
            ]);

            setSkus(skusList);
            setCategories(categoriesList);
            setModifiedSkus(skusList);

        } catch (err) {
            console.error('Error loading data:', err);
            setError(t('admin.skus.error.loadFailed', 'Failed to load SKUs'));
        } finally {
            setLoading(false);
        }
    }, [t]);

    useEffect(() => {
        loadData();
    }, [loadData]);

    // Optimized filter change handler with useCallback
    const handleFilterChange = useCallback((key: keyof Filters, value: string) => {
        setFilters(prev => ({ ...prev, [key]: value }));
    }, []);

    // Clear filters
    const clearFilters = useCallback(() => {
        setFilters({
            search: '',
            category: '',
            status: '',
            priceMin: '',
            priceMax: ''
        });
    }, []);

    // Optimized SKU selection handlers
    const handleSkuSelection = useCallback((sku: SKU) => {
        setSelectedSkus(prev => {
            const isSelected = prev.some(s => s.skuId === sku.skuId);
            if (isSelected) {
                return prev.filter(s => s.skuId !== sku.skuId);
            } else {
                return [...prev, sku];
            }
        });
    }, []);

    const handleSelectAll = useCallback((checked: boolean) => {
        if (checked) {
            setSelectedSkus(skusForDisplay);
        } else {
            setSelectedSkus([]);
        }
    }, [skusForDisplay]);

    // Bulk status change using unified service
    const handleBulkStatusChange = useCallback(async (newStatus: 'active' | 'inactive') => {
        if (selectedSkus.length === 0) return;

        try {
            // Use unified service for proper parent-child synchronization
            await SkuOperationsService.updateSkuStatus(
                selectedSkus.map(sku => sku.skuId),
                newStatus
            );

            // Update local state to reflect changes
            const newStatusValue = newStatus === 'active' ? 1 : 0;
            const currentSkus = hasImportedChanges ? importedSkus : modifiedSkus;

            const updatedSkus = currentSkus.map(sku => {
                if (selectedSkus.some(selected => selected.skuId === sku.skuId)) {
                    return { ...sku, isActive: newStatusValue };
                }
                return sku;
            });

            if (hasImportedChanges) {
                setImportedSkus(updatedSkus);
                setHasImportedChanges(true);
            } else {
                setModifiedSkus(updatedSkus);
                setHasUnsavedChanges(true);
            }
            setSelectedSkus([]); // Clear selection after status change

            toast.success(t('admin.skus.bulkActions.success', `${selectedSkus.length} SKUs updated successfully`), {
                position: 'top-right',
                autoClose: 3000
            });

        } catch (error) {
            console.error(`Error updating SKU status to ${newStatus}:`, error);
            toast.error(t('admin.skus.bulkActions.error', 'Failed to update SKU status. Please try again.'), {
                position: 'top-right',
                autoClose: 5000
            });
        }
    }, [selectedSkus, hasImportedChanges, importedSkus, modifiedSkus, t]);

    // Save changes (persists to backend)
    const handleSave = useCallback(async () => {
        setIsSaving(true);
        try {
            // Use unified service for consistent data handling
            const skusToSave = hasImportedChanges ? importedSkus : modifiedSkus;
            const operation: BulkOperation = {
                type: 'bulkEdit',
                fields: {} // No specific fields to update, just save as-is
            };

            await SkuOperationsService.saveBulkSkus(skusToSave, operation);

            setSkus(skusToSave); // Update the original base SKUs
            setHasUnsavedChanges(false);
            setHasImportedChanges(false);
            setSelectedSkus([]);
            toast.success(t('admin.skus.save.success', 'SKU changes saved successfully'), {
                position: 'top-right',
                autoClose: 3000
            });
        } catch (error) {
            console.error('Error saving SKU changes:', error);
            toast.error(t('admin.skus.save.error', 'Failed to save SKU changes. Please try again.'), {
                position: 'top-right',
                autoClose: 5000
            });
        } finally {
            setIsSaving(false);
        }
    }, [hasImportedChanges, importedSkus, modifiedSkus, t]);

    // Discard changes
    const handleDiscardChanges = useCallback(() => {
        setModifiedSkus(skus);
        setHasUnsavedChanges(false);
        setSelectedSkus([]);
    }, [skus]);

    // Export handler
    const handleExport = useCallback(async () => {
        try {
            setLoading(true);

            // Validate source data before export (but only show critical errors, not warnings)
            const validation = SkuImportExportService.validateSourceSkus(skus);

            if (!validation.isValid) {
                toast.error(t('admin.skus.export.validationError', `Export failed due to data validation errors: {{errors}}`, { errors: validation.errors.join(', ') }), {
                    position: 'top-right',
                    autoClose: 5000
                });
                return;
            }

            // Log warnings but don't show popup - silently clean the data
            if (validation.warnings.length > 0) {
                console.log('Export warnings (auto-cleaning):', validation.warnings);
            }

            const flattenedData = SkuImportExportService.flattenSkusForExport(skus);

            // Get the template for column definitions
            const template = templateService.getTemplate('sku-complete');
            if (!template) {
                throw new Error('SKU export template not found');
            }

            await excelService.generateCSV(
                flattenedData,
                template.columns,
                `skus-export-${new Date().toISOString().split('T')[0]}.csv`,
                {
                    includeInstructions: true,
                    instructions: template.instructions
                }
            );

            toast.success(t('admin.skus.export.success', 'Export completed successfully'), {
                position: 'top-right',
                autoClose: 3000
            });
        } catch (error) {
            console.error('Export error:', error);
            toast.error(t('admin.skus.export.error', 'Export failed. Please try again.'), {
                position: 'top-right',
                autoClose: 5000
            });
        } finally {
            setLoading(false);
        }
    }, [skus, t]);

    const handleImport = useCallback(async (optionId: string, file: File): Promise<void> => {
        try {
            console.log('Importing SKUs with template:', optionId, 'File:', file.name);

            // Get the template
            const template = templateService.getTemplate(optionId);
            if (!template) {
                throw new Error('Template not found');
            }

            // Parse CSV file
            const parseResult = await excelService.parseCSVFile(file, template.columns);
            console.log('Parsed import data:', parseResult);
            console.log('Number of parsed rows:', parseResult.data.length);

            if (parseResult.errors.length > 0) {
                console.error('Parse errors:', parseResult.errors);
                throw new Error(`Parse failed: ${parseResult.errors.map(e => `Row ${e.row}: ${e.message}`).join(', ')}`);
            }

            // Log sample of parsed data for debugging
            if (parseResult.data.length > 0) {
                console.log('Sample parsed SKU:', parseResult.data[0]);
                console.log('Data types check:', {
                    skuId: typeof parseResult.data[0].skuId,
                    type: typeof parseResult.data[0].type,
                    parentId: typeof parseResult.data[0].parentId,
                    nameEn: typeof parseResult.data[0].nameEn,
                    costPrice: typeof parseResult.data[0].costPrice
                });
            }

            // Validate the imported data
            const validation = SkuImportExportService.validateFlattenedSkus(parseResult.data);

            if (!validation.isValid) {
                console.error('Validation errors count:', validation.errors.length);
                console.error('First 10 validation errors:', validation.errors.slice(0, 10));

                // Group errors by type for better understanding
                const errorTypes = validation.errors.reduce((acc: Record<string, number>, error: string) => {
                    const type = error.split(':')[1]?.trim().split(' ')[0] || 'Unknown';
                    acc[type] = (acc[type] || 0) + 1;
                    return acc;
                }, {} as Record<string, number>);

                console.error('Error types breakdown:', errorTypes);

                // Show a more user-friendly error message
                const errorSummary = Object.entries(errorTypes)
                    .map(([type, count]) => `${type}: ${count}`)
                    .join(', ');

                throw new Error(`Validation failed with ${validation.errors.length} errors. Error breakdown: ${errorSummary}. Check console for details.`);
            }

            // Convert flattened data to nested SKU structure
            const nestedSkus = SkuImportExportService.nestSkusFromImport(parseResult.data);
            console.log('Nested SKUs:', nestedSkus);
            console.log('Number of nested SKUs:', nestedSkus.length);

            // Set import preview
            setImportedSkus(nestedSkus);
            setHasImportedChanges(true);

            console.log('Import preview loaded successfully');
        } catch (error) {
            console.error('Import failed:', error);
            throw error; // Re-throw to let ImportExportPanel handle the error display
        }
    }, []);

    const handleSaveImport = async () => {
        if (!hasImportedChanges || importedSkus.length === 0) return;
        // Don't save to backend here. Just apply to working set.
        setModifiedSkus(importedSkus); // Imported data becomes the current working set
        setHasImportedChanges(false); // No longer in "import preview" mode
        setHasUnsavedChanges(true);   // These imported changes are now "unsaved changes"
        setImportedSkus([]);          // Clear the temporary import holding state

        toast.info('Imported changes applied. Review and click Save to persist them.', {
            position: 'top-right',
            autoClose: 4000
        });
    };

    const handleDiscardImport = () => {
        setHasImportedChanges(false);
        setImportedSkus([]);
        // No need to revert modifiedSkus here, as they weren't touched by the import preview directly.
        // The table will automatically re-render with modifiedSkus because currentDisplaySkus will change.
        toast.info('Import discarded.', {
            position: 'top-right',
            autoClose: 3000
        });
    };

    // Get import/export options
    const exportOptions = templateService.getTemplateOptions('skus').filter(t => t.id === 'sku-complete');
    const importOptions = exportOptions;

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex flex-col items-center justify-center min-h-screen">
                <div className="text-red-600 text-lg mb-4">{error}</div>
                <button
                    onClick={loadData}
                    className="text-blue-600 hover:text-blue-800 underline"
                >
                    {t('admin.actions.tryAgain', 'Try Again')}
                </button>
            </div>
        );
    }

    return (
        <AdminGuard requiredPermission="manageSku">
            <div className="space-y-6">
                {/* Header */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">
                                {t('admin.skus.title', 'SKU Management')}
                            </h1>
                            <p className="mt-1 text-sm text-gray-600">
                                {t('admin.skus.description', 'Manage all SKUs across categories. View, edit, and organize your product inventory.')}
                            </p>
                        </div>
                        <div className="flex items-center space-x-3">
                            {/* Import/Export Panel */}
                            <ImportExportPanel
                                exportOptions={exportOptions}
                                onExport={handleExport}
                                importOptions={importOptions}
                                onImport={handleImport}
                                t={t}
                            />
                            <Link
                                href={addNavigationParams("/admin/skus/create", { from: 'list' })}
                                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center space-x-2 transition-colors"
                            >
                                <PlusIcon className="h-5 w-5" />
                                <span>{t('admin.skus.actions.create', 'Create SKU')}</span>
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Filters Section */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <button
                                    onClick={() => setShowFilters(!showFilters)}
                                    className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
                                >
                                    <FunnelIcon className="h-5 w-5" />
                                    <span>{t('admin.filters.title', 'Filters')}</span>
                                    <ChevronDownIcon className={`h-4 w-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
                                </button>
                                {(filters.search || filters.category || filters.status || filters.priceMin || filters.priceMax) && (
                                    <button
                                        onClick={clearFilters}
                                        className="text-sm text-blue-600 hover:text-blue-800"
                                    >
                                        {t('admin.filters.clear', 'Clear Filters')}
                                    </button>
                                )}
                            </div>
                            <div className="text-sm text-gray-500">
                                {t('admin.skus.count', '{{filtered}} of {{total}} SKUs', {
                                    filtered: filteredSkus.length,
                                    total: currentDisplaySkus.length
                                })}
                            </div>
                        </div>
                    </div>

                    {showFilters && (
                        <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        {t('admin.filters.search', 'Search')}
                                    </label>
                                    <div className="relative">
                                        <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
                                        <input
                                            type="text"
                                            placeholder={t('admin.skus.filters.searchPlaceholder', 'Search SKUs...')}
                                            value={filters.search}
                                            onChange={(e) => handleFilterChange('search', e.target.value)}
                                            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        {t('admin.filters.category', 'Category')}
                                    </label>
                                    <select
                                        value={filters.category}
                                        onChange={(e) => handleFilterChange('category', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="">{t('admin.filters.allCategories', 'All Categories')}</option>
                                        {categories.map(category => (
                                            <option key={category.id} value={category.id}>
                                                {category.name.en}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        {t('admin.filters.status', 'Status')}
                                    </label>
                                    <select
                                        value={filters.status}
                                        onChange={(e) => handleFilterChange('status', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="">{t('admin.filters.allStatuses', 'All Statuses')}</option>
                                        {SKU_STATUSES.map(status => (
                                            <option key={status.value} value={status.value}>
                                                {t(`admin.status.${status.value}`, status.label)}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        {t('admin.filters.minPrice', 'Min Price (₹)')}
                                    </label>
                                    <input
                                        type="number"
                                        placeholder="0"
                                        value={filters.priceMin}
                                        onChange={(e) => handleFilterChange('priceMin', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        {t('admin.filters.maxPrice', 'Max Price (₹)')}
                                    </label>
                                    <input
                                        type="number"
                                        placeholder="10000"
                                        value={filters.priceMax}
                                        onChange={(e) => handleFilterChange('priceMax', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    />
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Import Preview */}
                {
                    hasImportedChanges && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                            <div className="flex items-center justify-between mb-4">
                                <div>
                                    <h3 className="text-lg font-medium text-yellow-800">
                                        {t('admin.importExport.previewChanges')}
                                    </h3>
                                    <p className="text-sm text-yellow-700">
                                        {t('admin.importExport.importWarning')}
                                    </p>
                                </div>
                                <div className="flex items-center space-x-3">
                                    <button
                                        onClick={handleDiscardImport}
                                        className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                        disabled={isSaving}
                                    >
                                        {t('admin.actions.cancel')}
                                    </button>
                                    <button
                                        onClick={handleSave}
                                        className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
                                        disabled={isSaving}
                                    >
                                        {isSaving ? t('admin.importExport.importing') : t('admin.importExport.applyChanges')}
                                    </button>
                                </div>
                            </div>
                            <div className="text-sm text-yellow-700">
                                <p>{t('admin.skus.importPreview.count', '{{count}} SKUs ready to be imported', { count: importedSkus.length })}</p>
                            </div>
                        </div>
                    )
                }

                {/* Bulk Actions */}
                {
                    selectedSkus.length > 0 && (
                        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                    <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span className="text-sm font-medium text-blue-900">
                                        {t('admin.skus.bulkActions.selected', '{{count}} SKU{{plural}} selected', {
                                            count: selectedSkus.length,
                                            plural: selectedSkus.length === 1 ? '' : 's'
                                        })}
                                    </span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <button
                                        onClick={() => handleBulkStatusChange('active')}
                                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200"
                                    >
                                        {t('admin.actions.activate', 'Activate')}
                                    </button>
                                    <button
                                        onClick={() => handleBulkStatusChange('inactive')}
                                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-gray-700 bg-gray-100 hover:bg-gray-200"
                                    >
                                        {t('admin.actions.deactivate', 'Deactivate')}
                                    </button>
                                    <button
                                        onClick={() => setSelectedSkus([])}
                                        className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                                    >
                                        {t('admin.actions.clearSelection', 'Clear Selection')}
                                    </button>
                                </div>
                            </div>
                        </div>
                    )
                }

                {/* Save/Discard Changes Bar for Manual Changes */}
                {
                    hasUnsavedChanges && !hasImportedChanges && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                    <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                    </svg>
                                    <span className="text-sm font-medium text-yellow-900">
                                        {t('admin.skus.unsavedChanges', 'You have unsaved changes to SKUs')}
                                    </span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <button
                                        onClick={handleDiscardChanges}
                                        className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                                        disabled={isSaving}
                                    >
                                        {t('admin.actions.discard', 'Discard')}
                                    </button>
                                    <button
                                        onClick={handleSave}
                                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                                        disabled={isSaving}
                                    >
                                        {isSaving ? (
                                            <>
                                                <svg className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" fill="none" viewBox="0 0 24 24">
                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                                {t('admin.actions.saving', 'Saving...')}
                                            </>
                                        ) : (
                                            t('admin.actions.save', 'Save')
                                        )}
                                    </button>
                                </div>
                            </div>
                        </div>
                    )
                }

                {/* SKUs Table */}
                <div className="bg-white rounded-lg shadow">
                    <div className="overflow-x-auto">
                        {skusForDisplay.length === 0 ? (
                            <div className="text-center py-12">
                                <p className="text-gray-500">
                                    {filters.search || filters.category || filters.status || filters.priceMin || filters.priceMax
                                        ? t('admin.skus.table.noResultsFiltered', 'No SKUs match your current filters.')
                                        : t('admin.skus.table.noResults', 'No SKUs found.')
                                    }
                                </p>
                                {!filters.search && !filters.category && !filters.status && !filters.priceMin && !filters.priceMax && (
                                    <Link
                                        href={addNavigationParams("/admin/skus/create", { from: 'list' })}
                                        className="mt-2 text-blue-600 hover:text-blue-800 underline"
                                    >
                                        {t('admin.skus.table.createFirst', 'Create your first SKU')}
                                    </Link>
                                )}
                            </div>
                        ) : (
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <input
                                                type="checkbox"
                                                checked={selectedSkus.length === skusForDisplay.length && skusForDisplay.length > 0}
                                                onChange={(e) => handleSelectAll(e.target.checked)}
                                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                            />
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {t('admin.skus.table.columns.sku', 'SKU')}
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {t('admin.skus.table.columns.name', 'Name')}
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {t('admin.skus.table.columns.type', 'Type')}
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {t('admin.skus.table.columns.categories', 'Categories')}
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {t('admin.skus.table.columns.price', 'Price')}
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {t('admin.skus.table.columns.status', 'Status')}
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {t('admin.skus.table.columns.variants', 'Variants')}
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {t('admin.skus.table.columns.actions', 'Actions')}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {skusForDisplay.map((sku, index) => {

                                        return (
                                            <SkuTableRow
                                                key={`sku-row-${index}`}
                                                sku={sku}
                                                isSelected={selectedSkus.some(s => s.skuId === sku.skuId)}
                                                onToggleSelection={handleSkuSelection}
                                                getSkuCategories={getSkuCategories}
                                                t={t}
                                            />
                                        );
                                    })}
                                </tbody>
                            </table>
                        )}
                    </div>
                </div>
            </div>
        </AdminGuard>
    );
}

// Memoized table row component for better performance
interface SkuTableRowProps {
    sku: DisplaySKU;
    isSelected: boolean;
    onToggleSelection: (sku: SKU) => void;
    getSkuCategories: (skuId: number) => Category[];
    t: any; // Use any to match react-i18next TFunction interface
}

const SkuTableRow = React.memo<SkuTableRowProps>(({
    sku,
    isSelected,
    onToggleSelection,
    getSkuCategories,
    t
}) => {
    const skuCategories = useMemo(() => getSkuCategories(sku.skuId), [getSkuCategories, sku.skuId]);

    const handleCheckboxChange = useCallback(() => {
        onToggleSelection(sku);
    }, [onToggleSelection, sku]);

    return (
        <tr className={`hover:bg-gray-50 ${sku.isChildDisplay ? 'bg-gray-50' : ''}`}>
            <td className="px-6 py-4 whitespace-nowrap">
                <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={handleCheckboxChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center" style={{ paddingLeft: `${(sku.depth || 0) * 20}px` }}>
                    {sku.isChildDisplay && <span className="mr-2">└─</span>}
                    <div className="h-10 w-10 flex-shrink-0">
                        <Image
                            src={sku.imageUrl || '/placeholder-product.png'}
                            alt={sku.name.en}
                            width={40}
                            height={40}
                            className="h-10 w-10 rounded-lg object-cover"
                        />
                    </div>
                    <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">#{sku.skuId}</div>
                    </div>
                </div>
            </td>
            <td className="px-6 py-4">
                <div className="text-sm font-medium text-gray-900">{sku.name.en}</div>
                {sku.name.ta && (
                    <div className="text-sm text-gray-500">{sku.name.ta}</div>
                )}
                {/* Show variant name for child SKUs */}
                {sku.type === 'child' && sku.variantName && (
                    <div className="text-xs text-blue-600 mt-1">
                        Variant: {sku.variantName.en}
                        {sku.variantName.ta && sku.variantName.ta !== sku.variantName.en && (
                            <span className="text-gray-500"> ({sku.variantName.ta})</span>
                        )}
                    </div>
                )}
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${sku.type === 'parent' ? 'bg-yellow-100 text-yellow-800' : 'bg-indigo-100 text-indigo-800'
                    }`}>
                    {t(`admin.skus.type.${sku.type}`, sku.type === 'parent' ? 'Parent' : 'Child')}
                </span>
            </td>
            <td className="px-6 py-4">
                <div className="flex flex-wrap gap-1">
                    {skuCategories.slice(0, 2).map((category, categoryIndex) => {

                        return (
                            <span
                                key={`category-${category.id}-sku-${sku.skuId}-${categoryIndex}`}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                            >
                                {category.name.en}
                            </span>
                        );
                    })}
                    {skuCategories.length > 2 && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {t('admin.skus.table.moreCategories', '+{{count}} more', { count: skuCategories.length - 2 })}
                        </span>
                    )}
                </div>
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                {sku.type === 'parent' ? (
                    <div className="text-sm text-gray-500">
                        -
                    </div>
                ) : (
                    <div className="space-y-1">
                        <div className="text-sm text-gray-900">{t('admin.skus.table.price.mrp', 'MRP')}: ₹{sku.mrp}</div>
                        <div className="text-sm text-gray-600">{t('admin.skus.table.price.cp', 'CP')}: ₹{sku.costPrice}</div>
                        <div className="text-sm text-gray-600">{t('admin.skus.table.price.sp', 'SP')}: ₹{sku.sellingPrice}</div>
                    </div>
                )}
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${sku.isActive === 1
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                    }`}>
                    {t(`admin.status.${sku.isActive === 1 ? 'active' : 'inactive'}`, sku.isActive === 1 ? 'Active' : 'Inactive')}
                </span>
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                {sku.type === 'parent' ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {t('admin.skus.table.variantsCount', '{{count}} variants', { count: sku.variants?.length ?? 0 })}
                    </span>
                ) : (
                    <span className="text-sm text-gray-500">
                        -
                    </span>
                )}
            </td>
            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div className="flex items-center space-x-2">
                    <Link
                        href={`/admin/skus/${sku.skuId}`}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title={t('admin.skus.actions.view', 'View SKU')}
                    >
                        <EyeIcon className="h-4 w-4" />
                    </Link>
                    <Link
                        href={addNavigationParams(`/admin/skus/${sku.skuId}/edit`, { from: 'list' })}
                        className="text-green-600 hover:text-green-900 p-1 rounded"
                        title={t('admin.skus.actions.edit', 'Edit SKU')}
                    >
                        <PencilIcon className="h-4 w-4" />
                    </Link>
                </div>
            </td>
        </tr>
    );
});

SkuTableRow.displayName = 'SkuTableRow';