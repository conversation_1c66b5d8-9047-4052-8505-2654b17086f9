"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { upsertCategories, getOrderedCategories } from '../../services/categoryService';
import { Category } from '../../types/category';
import { AdminGuard } from '../../components/common/AdminGuard';
import SortableCategoriesTable from '../components/SortableCategoriesTable';
import ImportExportPanel from '../components/ImportExportPanel';
import { templateService } from '../../services/templateService';
import { excelService } from '../../services/excelService';
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';
import { v4 as uuidv4 } from 'uuid';

const CategoriesPage: React.FC = () => {
    const { t } = useTranslation();
    const queryClient = useQueryClient();
    const [selectedCategories, setSelectedCategories] = useState<Category[]>([]);
    const [reorderedCategories, setReorderedCategories] = useState<Category[]>([]);
    const [importedCategories, setImportedCategories] = useState<Category[]>([]);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const [hasImportedChanges, setHasImportedChanges] = useState(false);
    const [isSaving, setIsSaving] = useState(false);

    const {
        data: categories,
        isLoading,
        isError,
        error,
    } = useQuery<Category[], Error>({
        queryKey: ['admin-categories'],
        queryFn: async () => {
            try {
                // Direct service call with includeInactive for admin - returns ordered array directly
                return await getOrderedCategories({ includeInactive: true, forceRefresh: true });
            } catch (error) {
                console.error('Error loading admin categories:', error);
                return [];
            }
        },
        staleTime: 0, // Always consider data stale for admin
        gcTime: 0, // Don't cache data for admin (renamed from cacheTime)
        refetchOnMount: true, // Always refetch when component mounts
        refetchOnWindowFocus: false, // Don't refetch on window focus
    });

    // Sync reorderedCategories with fetched categories data
    useEffect(() => {
        if (categories && categories.length > 0) {
            setReorderedCategories(categories);
        }
    }, [categories]);

    const handleBulkStatusChange = (status: 'active' | 'inactive') => {
        const newStatus = status === 'active' ? 1 : 0;

        // Update the status of selected categories
        const updatedCategories = reorderedCategories.map(category => {
            if (selectedCategories.some(selected => selected.id === category.id)) {
                return { ...category, isActive: newStatus };
            }
            return category;
        });

        setReorderedCategories(updatedCategories);
        setHasUnsavedChanges(true);
        setSelectedCategories([]); // Clear selection after status change

        console.log(`Bulk ${status} for categories:`, selectedCategories.map(c => c.id));
    };

    const handleReorder = (newOrder: Category[]) => {
        setReorderedCategories(newOrder);
        setHasUnsavedChanges(true);
    };

    const handleSave = async () => {
        setIsSaving(true);
        try {
            console.log('Saving category changes:', reorderedCategories.map((cat, index) => ({
                id: cat.id,
                order: index,
                isActive: cat.isActive,
                isNew: cat.id < 0 // Negative IDs indicate new categories
            })));

            // Call the upsert API with reordered categories
            await upsertCategories(reorderedCategories);

            setHasUnsavedChanges(false);

            // Invalidate and refetch categories to get the latest data from backend
            await queryClient.invalidateQueries({ queryKey: ['admin-categories'] });

            console.log('Category changes saved successfully');

            toast.success(t('admin.actions.save') + ' successful', {
                position: "top-right",
                autoClose: 3000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
            });
            // TODO: Show success notification
        } catch (error) {
            console.error('Error saving category changes:', error);
            toast.error(t('common.unknownError'), {
                position: "top-right",
                autoClose: 3000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
            });
        } finally {
            setIsSaving(false);
        }
    };

    const handleSaveImport = async () => {
        setIsSaving(true);
        try {
            console.log('Saving imported categories:', importedCategories);

            // Call the upsert API with imported categories
            await upsertCategories(importedCategories);

            // Clear import state
            setImportedCategories([]);
            setHasImportedChanges(false);

            // Invalidate and refetch categories to get the latest data from backend
            await queryClient.invalidateQueries({ queryKey: ['admin-categories'] });

            console.log('Categories imported successfully');
            toast.success(t('admin.importExport.import') + ' successful', {
                position: "top-right",
                autoClose: 3000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
            });
        } catch (error) {
            console.error('Error saving imported categories:', error);
            toast.error(t('common.unknownError'), {
                position: "top-right",
                autoClose: 3000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
            });
        } finally {
            setIsSaving(false);
        }
    };

    const handleDiscardChanges = () => {
        if (categories) {
            setReorderedCategories(categories);
            setHasUnsavedChanges(false);
        }
    };

    const handleDiscardImport = () => {
        setImportedCategories([]);
        setHasImportedChanges(false);
        if (categories) {
            setReorderedCategories(categories);
        }
    };

    // Import/Export functionality
    const handleExport = async (optionId: string) => {
        try {
            // Always export all categories (no filtering)
            const exportData = reorderedCategories;

            switch (optionId) {
                case 'category-list':
                    const categoryTemplate = templateService.getTemplate('category-list');
                    if (categoryTemplate) {
                        const formattedData = exportData.map(category => ({
                            id: category.id,
                            nameEn: category.name?.en || '',
                            nameTa: category.name?.ta || '',
                            icon: category.icon || '',
                            background: category.background || 'transparent',
                            status: category.isActive === 1 ? 'active' : 'inactive', // Convert isActive to status string
                            skuIds: category.skuIds ? category.skuIds.join(':') : '' // Export SKU IDs as colon-separated string
                        }));

                        await excelService.generateCSV(
                            formattedData,
                            categoryTemplate.columns,
                            `categories_${new Date().toISOString().split('T')[0]}.csv`,
                            {
                                includeInstructions: true,
                                instructions: [
                                    'This file contains exported category data from the admin portal',
                                    'You can modify the data and re-import it using the Import button',
                                    'Required fields: Name (English) - must be provided for all categories',
                                    'Optional fields: Category ID, Name (Tamil), Icon, Background Color, Status, SKU IDs',
                                    'Leave Category ID empty for new categories (will be auto-generated)',
                                    'Status values: active, inactive (defaults to inactive for new categories)',
                                    'Background Color: Use "transparent" for no background, or hex color codes (e.g., #3B82F6)',
                                    'SKU IDs: Colon-separated list of SKU IDs (e.g., "101:102:103") for category-SKU mappings',
                                    'Category order will be determined by CSV row order and can be adjusted via drag-and-drop in the UI',
                                    'Remove all comment lines (starting with #) before importing'
                                ]
                            }
                        );
                    }
                    break;

                default:
                    console.warn('Unknown export option:', optionId);
            }
        } catch (error) {
            console.error('Export failed:', error);
            // TODO: Show error notification
        }
    };

    const handleImport = async (optionId: string, file: File): Promise<void> => {
        try {
            const template = templateService.getTemplate(optionId);
            if (!template) {
                throw new Error('Template not found');
            }

            const parsedData = await excelService.parseFile(file, template.columns);

            if (parsedData.errors.length > 0) {
                console.warn('Import validation errors:', parsedData.errors);
                // For now, we'll proceed with warnings but still import valid data
                // TODO: Show validation errors to user as warnings
            }

            // Convert parsed data to Category format
            const importedCategoriesData: Category[] = parsedData.data.map((row, index) => ({
                id: row.id || -(1000 + index), // Use existing ID or generate negative ID for new categories
                versionUuid: uuidv4(),
                name: {
                    en: row.nameEn || `Category ${index + 1}`, // Provide default name if missing
                    ta: row.nameTa || ''
                },
                icon: row.icon || 'folder', // Default icon
                background: row.background === 'transparent' ? 'transparent' : (row.background || 'transparent'), // Handle transparent background
                skuIds: row.skuIds ? row.skuIds.split(':').map((id: string) => parseInt(id.trim())).filter((id: number) => !isNaN(id)) : [], // Parse colon-separated SKU IDs
                isActive: row.status === 'active' ? 1 : 0, // Convert status to isActive (1 for active, 0 for inactive)
                orderNo: index + 1 // Auto-assign order based on CSV row position
            }));

            // Update UI with imported data
            setImportedCategories(importedCategoriesData);
            setReorderedCategories(importedCategoriesData);
            setHasImportedChanges(true);

            console.log('Import preview loaded:', importedCategoriesData);
        } catch (error) {
            console.error('Import failed:', error);
            // TODO: Show error message to user
            throw error;
        }
    };

    // Get import/export options - just the primary category list option
    const exportOptions = templateService.getTemplateOptions('categories').filter(t => t.id === 'category-list');
    const importOptions = exportOptions;

    if (isError) {
        return (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                    <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">{t('common.error')}</h3>
                        <div className="mt-2 text-sm text-red-700">
                            {error?.message || t('common.unknownError')}
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <AdminGuard requiredPermission="manageCategories">
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-semibold text-gray-900">{t('admin.categories.title')}</h1>
                        <p className="mt-1 text-sm text-gray-600">
                            {t('admin.categories.subtitle')}
                        </p>
                    </div>
                    <div className="flex items-center space-x-3">
                        <ImportExportPanel
                            exportOptions={exportOptions}
                            onExport={handleExport}
                            importOptions={importOptions}
                            onImport={handleImport}
                            t={(key: string) => key.split('.').pop() || key} // Simple translation fallback
                        />
                        <Link
                            href="/admin/categories/create"
                            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            {t('admin.actions.create')} {t('common.category')}
                        </Link>
                    </div>
                </div>

                {/* Bulk Actions */}
                {selectedCategories.length > 0 && (
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span className="text-sm font-medium text-blue-900">
                                    {selectedCategories.length} {selectedCategories.length === 1 ? t('common.category') : t('common.categories')} {t('admin.actions.selected')}
                                </span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <button
                                    onClick={() => handleBulkStatusChange('active')}
                                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200"
                                >
                                    {t('admin.actions.activate')}
                                </button>
                                <button
                                    onClick={() => handleBulkStatusChange('inactive')}
                                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-gray-700 bg-gray-100 hover:bg-gray-200"
                                >
                                    {t('admin.actions.deactivate')}
                                </button>
                                <button
                                    onClick={() => setSelectedCategories([])}
                                    className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    {t('admin.actions.clearSelection')}
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                {/* Save/Discard Changes Bar for Reordering */}
                {hasUnsavedChanges && !hasImportedChanges && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                                <span className="text-sm font-medium text-yellow-900">
                                    {t('admin.actions.unsavedChanges')}
                                </span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <button
                                    onClick={handleDiscardChanges}
                                    className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                                    disabled={isSaving}
                                >
                                    {t('admin.actions.discard')}
                                </button>
                                <button
                                    onClick={handleSave}
                                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                                    disabled={isSaving}
                                >
                                    {isSaving ? (
                                        <>
                                            <svg className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            {t('admin.actions.saving')}
                                        </>
                                    ) : (
                                        t('admin.actions.save')
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                {/* Save/Discard Changes Bar for Import */}
                {hasImportedChanges && (
                    <div className="bg-green-50 border border-green-200 rounded-md p-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span className="text-sm font-medium text-green-900">
                                    {t('admin.importExport.previewChanges')} ({importedCategories.length} {t('common.categories')}). {t('admin.importExport.importWarning')}
                                </span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <button
                                    onClick={handleDiscardImport}
                                    className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                                    disabled={isSaving}
                                >
                                    {t('admin.actions.discard')} {t('admin.importExport.import')}
                                </button>
                                <button
                                    onClick={handleSaveImport}
                                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
                                    disabled={isSaving}
                                >
                                    {isSaving ? (
                                        <>
                                            <svg className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            {t('admin.actions.saving')} {t('admin.importExport.import')}...
                                        </>
                                    ) : (
                                        t('admin.actions.save') + ' ' + t('admin.importExport.import')
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                {/* Categories Table */}
                <SortableCategoriesTable
                    categories={reorderedCategories}
                    selectedCategories={selectedCategories}
                    onSelectionChange={setSelectedCategories}
                    onReorder={handleReorder}
                    loading={isLoading}
                />
            </div>
        </AdminGuard>
    );
};

export default CategoriesPage;