"use client";

import { AdminGuard } from "../components/common/AdminGuard";
import AdminSidebar from "./components/AdminSidebar";
import AdminHeader from "./components/AdminHeader";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../context/AuthContext";
import { useAdminPermissions } from "../hooks/useAdminPermissions";
import { toast } from "react-toastify";

// Note: Metadata cannot be exported from client components in Next.js App Router
// The metadata will be handled by the root layout or individual pages

export default function AdminLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [isRefreshing, setIsRefreshing] = useState(false);

    const router = useRouter();
    const { isUserLoggedIn, userLogout } = useAuth();
    const { loadAdminRoles } = useAdminPermissions();

    // Block admin access in customer mode (production/test)
    useEffect(() => {
        if (process.env.NEXT_PUBLIC_APP_MODE === 'customer') {
            // Redirect to home page or show not found
            window.location.href = '/';
            return;
        }
    }, []);

    // Close mobile menu on screen resize to desktop
    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth >= 768 && isMobileMenuOpen) {
                setIsMobileMenuOpen(false);
            }
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [isMobileMenuOpen]);

    // Close mobile menu on escape key
    useEffect(() => {
        const handleEscapeKey = (event: KeyboardEvent) => {
            if (event.key === 'Escape' && isMobileMenuOpen) {
                setIsMobileMenuOpen(false);
            }
        };

        document.addEventListener('keydown', handleEscapeKey);
        return () => document.removeEventListener('keydown', handleEscapeKey);
    }, [isMobileMenuOpen]);

    // Button handlers for Access Denied page
    const handleGoHome = () => {
        router.push('/');
    };

    const handleLogout = async () => {
        try {
            await userLogout();
            toast.success('Logged out successfully');
            router.push('/');
        } catch (error) {
            console.error('Logout failed:', error);
            toast.error('Logout failed. Please try again.');
        }
    };

    const handleRefreshPermissions = async () => {
        setIsRefreshing(true);
        try {
            const success = await loadAdminRoles();
            if (success) {
                toast.success('Permissions refreshed successfully');
            } else {
                toast.warning('No admin permissions found for your account');
            }
        } catch (error) {
            console.error('Failed to refresh permissions:', error);
            toast.error('Failed to refresh permissions. Please try again.');
        } finally {
            setIsRefreshing(false);
        }
    };

    // Don't render admin in customer mode
    if (process.env.NEXT_PUBLIC_APP_MODE === 'customer') {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">Page Not Found</h1>
                    <p className="text-gray-600">The page you're looking for doesn't exist.</p>
                </div>
            </div>
        );
    }

    const closeMobileMenu = () => setIsMobileMenuOpen(false);
    const toggleMobileMenu = () => setIsMobileMenuOpen(!isMobileMenuOpen);

    return (
        <AdminGuard
            requireAdmin
            fallback={
                <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
                    <div className="text-center max-w-md mx-auto">
                        <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
                        <p className="text-gray-600 mb-8">You don&apos;t have permission to access the admin portal.</p>

                        {/* Action Buttons */}
                        <div className="space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                            {/* Primary Button - Go Home */}
                            <button
                                onClick={handleGoHome}
                                className="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 cursor-pointer"
                            >
                                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                </svg>
                                Home
                            </button>



                            {/* Secondary Button - Refresh Permissions */}
                            <button
                                onClick={handleRefreshPermissions}
                                disabled={isRefreshing}
                                className="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 hover:shadow-md hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-none"
                            >
                                {isRefreshing ? (
                                    <>
                                        <svg className="w-5 h-5 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z"></path>
                                        </svg>
                                        Refreshing...
                                    </>
                                ) : (
                                    <>
                                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                        </svg>
                                        Refresh Permissions
                                    </>
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            }
        >
            <div className="h-screen flex overflow-hidden bg-gray-50">
                {/* Mobile backdrop overlay */}
                {isMobileMenuOpen && (
                    <div className="fixed inset-0 z-40 md:hidden">
                        <div
                            className="fixed inset-0 bg-opacity-50 transition-opacity"
                            onClick={closeMobileMenu}
                            aria-hidden="true"
                        />
                    </div>
                )}

                {/* Responsive Sidebar */}
                <AdminSidebar
                    isMobileOpen={isMobileMenuOpen}
                    onCloseMobile={closeMobileMenu}
                />

                {/* Main Content Area */}
                <div className="flex-1 flex flex-col overflow-hidden">
                    {/* Admin Header */}
                    <AdminHeader onMobileMenuToggle={toggleMobileMenu} />

                    {/* Page Content */}
                    <main className="flex-1 overflow-auto p-2 sm:p-2">
                        {children}
                    </main>
                </div>
            </div>
        </AdminGuard>
    );
} 