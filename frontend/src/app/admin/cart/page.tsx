"use client";

import React, { useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { AdminGuard } from '../../components/common/AdminGuard';
import DataTable, { Column } from '../components/DataTable';
import StatusBadge from '../components/StatusBadge';
import {
    getCartsForAdmin,
    calculateAdminCartTotal,
    formatCartCreatedDate
} from '../../services/cartService';
import { getSkus } from '../../services/skuService';
import { AdminCart, ADMIN_CART_STATUSES } from '../../types/session';

import { useCartFilters } from '../../hooks/useCartFilters';
import { useTranslation } from 'react-i18next';
import { logger } from '@/lib/logger';
import DatePicker from '../../components/common/DatePicker';
import { ArrowPathIcon } from '@heroicons/react/24/outline';
import ExportButton from '../components/OrderExportButton';
import { templateService } from '../../services/templateService';
import { excelService } from '../../services/excelService';
import { getSkuById } from '../../services/skuService';
import { toast } from 'react-toastify';

const CartsPage: React.FC = () => {
    const router = useRouter();
    const { t } = useTranslation();
    const [isExporting, setIsExporting] = useState(false);

    // Use the new cart filters hook
    const {
        filters,
        currentPage,
        limit,
        updateFilter,
        updatePage,
        // resetFilters, // Available if needed for reset all functionality 
        clearValueFilters,
        getServerFilters,
        getClientFilters
    } = useCartFilters();

    // Get filters for server and client-side processing
    const serverFilters = getServerFilters();
    const clientFilters = getClientFilters();

    // Fetch carts data with pagination and server-side filtering
    const {
        data: cartsData,
        isLoading,
        isError,
        error,
        refetch,
        isFetching
    } = useQuery({
        queryKey: ['admin-carts', currentPage, limit, serverFilters],
        queryFn: () => getCartsForAdmin(currentPage, limit, serverFilters),
        staleTime: 0,
        gcTime: 0,
        refetchOnMount: true,
        refetchOnWindowFocus: false,
    });

    const carts = cartsData?.carts || [];
    const pagination = cartsData?.pagination;

    // Filter carts based on client-side filters only (server filters already applied)
    const filteredCarts = useMemo(() => {
        return carts.filter((cart: AdminCart) => {
            // Customer search filter
            const customerMatch = !clientFilters.customerSearch ||
                cart.cartJson.customer.name.toLowerCase().includes(clientFilters.customerSearch.toLowerCase()) ||
                cart.cartJson.customer.mobile.includes(clientFilters.customerSearch);

            // Cart value filter
            const cartValue = calculateAdminCartTotal(cart);
            const minValueMatch = clientFilters.minValue <= 0 || cartValue >= clientFilters.minValue;
            const maxValueMatch = clientFilters.maxValue <= 0 || cartValue <= clientFilters.maxValue;

            return customerMatch && minValueMatch && maxValueMatch;
        });
    }, [carts, clientFilters]);

    // Handle row click - navigate to cart details with returnFilters
    const handleRowClick = (cart: AdminCart) => {
        logger.debug('Cart clicked', { cartId: cart.cartId });

        // Create returnFilters parameter to preserve current filters and pagination
        const currentParams = new URLSearchParams();
        if (filters.status) currentParams.set('status', filters.status);
        if (filters.dateFrom) currentParams.set('dateFrom', filters.dateFrom);
        if (filters.dateTo) currentParams.set('dateTo', filters.dateTo);
        if (filters.customerSearch) currentParams.set('customerSearch', filters.customerSearch);
        if (filters.minValue > 0) currentParams.set('minValue', filters.minValue.toString());
        if (filters.maxValue > 0) currentParams.set('maxValue', filters.maxValue.toString());
        if (currentPage > 1) currentParams.set('page', currentPage.toString());
        if (limit !== 20) currentParams.set('limit', limit.toString());

        const returnFilters = encodeURIComponent(currentParams.toString());
        router.push(`/admin/cart/${cart.cartId}?returnFilters=${returnFilters}`);
    };

    // Handle refresh button click
    const handleRefresh = () => {
        logger.debug('Carts: Manual refresh triggered', {
            filters,
            serverFilters,
            clientFilters,
            currentPage,
            limit
        });
        refetch();
    };

    // Handle export functionality
    const handleExport = async (exportFiltered: boolean) => {
        setIsExporting(true);
        try {
            logger.debug('Carts export triggered', {
                exportFiltered,
                filteredCount: filteredCarts.length,
                totalCount: carts.length,
                filters
            });

            // Choose data to export
            const exportData = exportFiltered ? filteredCarts : carts;

            if (exportData.length === 0) {
                toast.error(t('admin.cart.export.error'));
                return;
            }

            // Step 1: Collect all unique SKU IDs from all carts
            const allSkuIds = new Set<number>();
            exportData.forEach(cart => {
                if (cart.cartJson.skuItems && cart.cartJson.skuItems.length > 0) {
                    cart.cartJson.skuItems.forEach(item => {
                        allSkuIds.add(item.skuId);
                    });
                }
            });

            // Step 2: Batch fetch all SKUs at once (optimized)
            const skuLookupMap: Record<number, any> = {};
            if (allSkuIds.size > 0) {
                try {
                    const uniqueSkuIds = Array.from(allSkuIds);
                    logger.debug('Cart export: Batch fetching SKUs', {
                        uniqueSkuCount: uniqueSkuIds.length,
                        totalCartItems: exportData.reduce((sum, cart) => sum + (cart.cartJson.skuItems?.length || 0), 0)
                    });

                    const batchSkus = await getSkus({ skuIds: uniqueSkuIds, allowInactive: true });
                    batchSkus.forEach((sku: any) => {
                        skuLookupMap[sku.skuId] = sku;
                    });
                } catch (error) {
                    logger.warn('Cart export: Batch SKU fetch failed, will use fallback names', { error });
                }
            }

            // Step 3: Format carts for export using pre-fetched SKUs
            const formattedData = exportData.map((cart) => {
                // Build SKU details string
                let skuDetails = '';
                let skuIds = '';

                if (cart.cartJson.skuItems && cart.cartJson.skuItems.length > 0) {
                    const skuDetailsArray = cart.cartJson.skuItems.map((item) => {
                        const sku = skuLookupMap[item.skuId];
                        const skuName = sku?.name?.en || item.skuName || `SKU#${item.skuId}`;
                        const quantity = item.quantity || 0;
                        const price = item.sellingPrice || 0;
                        return `SKU#${item.skuId}: ${skuName} (Qty: ${quantity}, ₹${price} each)`;
                    });

                    skuDetails = skuDetailsArray.join(', ');
                    skuIds = cart.cartJson.skuItems.map(item => item.skuId).join(',');
                }

                return {
                    cartId: cart.cartId || `#${cart.id}`,
                    status: cart.status,
                    customerName: cart.cartJson.customer.name || '',
                    customerMobile: cart.cartJson.customer.mobile || '',
                    itemsCount: cart.cartJson.skuItems?.length || 0,
                    totalValue: calculateAdminCartTotal(cart),
                    createdAt: formatCartCreatedDate(cart.rowCreatedAt),
                    skuDetails,
                    skuIds,
                    customerLocation: cart.cartJson.customer.location
                        ? `${cart.cartJson.customer.location.lat}, ${cart.cartJson.customer.location.lng}`
                        : '',
                    customerAddress: cart.cartJson.customer.address || '',
                };
            });

            // Get template for export
            const cartTemplate = templateService.getTemplate('cart-list');
            if (!cartTemplate) {
                throw new Error('Cart export template not found');
            }

            // Generate filename with filter info
            const dateStr = new Date().toISOString().split('T')[0];
            let filename = `carts_${dateStr}`;

            // Add filter info to filename
            if (filters.dateFrom && filters.dateTo) {
                const fromDate = filters.dateFrom.replace(/-/g, '');
                const toDate = filters.dateTo.replace(/-/g, '');
                filename = `carts_${fromDate}_to_${toDate}`;
            }

            if (exportFiltered && (filters.status || filters.customerSearch || filters.minValue > 0 || filters.maxValue > 0)) {
                if (filters.status) filename += `_${filters.status}`;
                if (filters.customerSearch) filename += `_filtered`;
            }

            filename += '.csv';

            // Generate CSV
            await excelService.generateCSV(
                formattedData,
                cartTemplate.columns,
                filename,
                {
                    includeInstructions: true,
                    instructions: [
                        ...cartTemplate.instructions || [],
                        `Export date: ${new Date().toLocaleDateString('en-IN')}`,
                        filters.dateFrom && filters.dateTo
                            ? `Date range: ${filters.dateFrom} to ${filters.dateTo}`
                            : 'Date range: All time',
                        exportFiltered
                            ? `Filtered data: ${filteredCarts.length} carts (Filters applied)`
                            : `All data: ${carts.length} carts`
                    ]
                }
            );

            toast.success(t('admin.cart.export.success'));
            logger.info('Carts export completed successfully', {
                exportCount: formattedData.length,
                exportFiltered
            });

        } catch (error) {
            logger.error('Carts export failed', { error });
            toast.error(t('admin.cart.export.error'));
        } finally {
            setIsExporting(false);
        }
    };

    // Handle date changes
    const handleDateFromChange = (value: string) => {
        updateFilter('dateFrom', value);
    };

    const handleDateToChange = (value: string) => {
        updateFilter('dateTo', value);
    };

    // Define table columns with proper sorting
    const columns: Column<AdminCart>[] = useMemo(() => [
        {
            key: 'id',
            header: 'Cart ID',
            width: '100px',
            render: (cart) => `#${cart.id}`,
            sortable: true,
            getSortValue: (cart) => cart.id
        },
        {
            key: 'customer',
            header: 'Customer',
            width: '200px',
            render: (cart) => (
                <div>
                    <div className="font-medium text-gray-900">
                        {cart.cartJson.customer.name || 'Unknown'}
                    </div>
                    <div className="text-sm text-gray-500">
                        {cart.cartJson.customer.mobile ? cart.cartJson.customer.mobile : 'No phone'}
                    </div>
                </div>
            ),
            sortable: true,
            getSortValue: (cart) => cart.cartJson.customer.name || ''
        },
        {
            key: 'itemsCount',
            header: 'Items',
            width: '80px',
            render: (cart) => cart.cartJson.skuItems.length.toString(),
            sortable: true,
            getSortValue: (cart) => cart.cartJson.skuItems.length
        },
        {
            key: 'totalValue',
            header: 'Cart Value',
            width: '120px',
            render: (cart) => `₹${calculateAdminCartTotal(cart).toFixed(2)}`,
            sortable: true,
            getSortValue: (cart) => calculateAdminCartTotal(cart)
        },
        {
            key: 'status',
            header: 'Status',
            width: '120px',
            render: (cart) => <StatusBadge status={cart.status.toLowerCase()} />,
            sortable: true,
            getSortValue: (cart) => cart.status
        },
        {
            key: 'rowCreatedAt',
            header: 'Created',
            width: '160px',
            render: (cart) => formatCartCreatedDate(cart.rowCreatedAt),
            sortable: true,
            getSortValue: (cart) => cart.rowCreatedAt
        },
        {
            key: 'actions',
            header: 'Actions',
            width: '120px',
            render: (cart) => (
                <div className="flex space-x-2 justify-end">
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            handleRowClick(cart);
                        }}
                        className="text-blue-600 hover:text-blue-900 text-sm"
                        title={t('admin.cart.list.actions.view')}
                    >
                        {t('admin.cart.list.actions.view')}
                    </button>
                </div>
            ),
            sortable: false
        }
    ], [t, handleRowClick]);

    if (isError) {
        return (
            <AdminGuard requiredPermission="viewAllCarts">
                <div className="p-6">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 className="text-lg font-medium text-red-800 mb-2">
                            Error Loading Carts
                        </h3>
                        <p className="text-red-600">
                            {error?.message || 'Failed to load carts. Please try again.'}
                        </p>
                        <button
                            onClick={() => refetch()}
                            className="mt-3 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                        >
                            Retry
                        </button>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    return (
        <AdminGuard requiredPermission="viewAllCarts">
            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">{t('admin.cart.title')}</h1>
                        <p className="text-gray-600 mt-1">{t('admin.cart.subtitle')}</p>
                    </div>
                    <div className="flex items-center space-x-3">
                        <ExportButton
                            onExport={handleExport}
                            filteredCount={filteredCarts.length}
                            totalCount={carts.length}
                            disabled={isExporting || isLoading}
                            entityName="cart"
                        />
                    </div>
                </div>

                {/* Filters */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Filters</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
                        {/* Status Filter */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                            <select
                                value={filters.status}
                                onChange={(e) => updateFilter('status', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                <option value="">All Statuses</option>
                                {ADMIN_CART_STATUSES.map(status => (
                                    <option key={status} value={status}>
                                        {status.charAt(0) + status.slice(1).toLowerCase()}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Date From */}
                        <div>
                            <DatePicker
                                label="From Date"
                                value={filters.dateFrom}
                                onChange={handleDateFromChange}
                                placeholder="Select start date"
                            />
                        </div>

                        {/* Date To */}
                        <div>
                            <DatePicker
                                label="To Date"
                                value={filters.dateTo}
                                onChange={handleDateToChange}
                                placeholder="Select end date"
                            />
                        </div>

                        {/* Customer Search */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Customer</label>
                            <input
                                type="text"
                                placeholder="Search name or phone"
                                value={filters.customerSearch}
                                onChange={(e) => updateFilter('customerSearch', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                        </div>

                        {/* Min Value */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Min Value (₹)</label>
                            <input
                                type="number"
                                min="0"
                                placeholder="0"
                                value={filters.minValue || ''}
                                onChange={(e) => updateFilter('minValue', parseInt(e.target.value) || 0)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                        </div>

                        {/* Max Value */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Max Value (₹)</label>
                            <input
                                type="number"
                                min="0"
                                placeholder="No limit"
                                value={filters.maxValue || ''}
                                onChange={(e) => updateFilter('maxValue', parseInt(e.target.value) || 0)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                        </div>
                    </div>

                    {/* Filter Summary */}
                    <div className="mt-4 flex items-center justify-between text-sm text-gray-600">
                        <div>
                            Showing {filteredCarts.length} of {carts.length} carts
                            {pagination && (
                                <span className="ml-2">
                                    (Page {pagination.currentPage} of {pagination.totalPages}, Total: {pagination.totalRows})
                                </span>
                            )}
                        </div>
                        <div className="flex items-center space-x-3">
                            {(filters.status || filters.customerSearch || filters.minValue > 0 || filters.maxValue > 0) && (
                                <button
                                    onClick={clearValueFilters}
                                    className="text-blue-600 hover:text-blue-800 font-medium"
                                >
                                    Clear Filters
                                </button>
                            )}
                            <button
                                onClick={handleRefresh}
                                disabled={isFetching}
                                className="flex items-center space-x-2 px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                <ArrowPathIcon className={`h-4 w-4 ${isFetching ? 'animate-spin' : ''}`} />
                                <span>{isFetching ? t('admin.cart.list.filters.refreshing') : t('admin.cart.list.filters.refresh')}</span>
                            </button>
                        </div>
                    </div>
                </div>

                {/* Carts Table */}
                <DataTable
                    data={filteredCarts}
                    columns={columns}
                    loading={isLoading}
                    emptyMessage="No carts found"
                    onRowClick={handleRowClick}
                    getItemId={(cart) => cart.id}
                />

                {/* Pagination */}
                {pagination && pagination.totalPages > 1 && (
                    <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow">
                        <div className="flex-1 flex justify-between sm:hidden">
                            <button
                                onClick={() => updatePage(Math.max(1, currentPage - 1))}
                                disabled={currentPage <= 1}
                                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Previous
                            </button>
                            <button
                                onClick={() => updatePage(Math.min(pagination.totalPages, currentPage + 1))}
                                disabled={currentPage >= pagination.totalPages}
                                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Next
                            </button>
                        </div>
                        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p className="text-sm text-gray-700">
                                    Showing page <span className="font-medium">{pagination.currentPage}</span> of{' '}
                                    <span className="font-medium">{pagination.totalPages}</span> pages
                                    <span className="ml-2">({pagination.totalRows} total carts)</span>
                                </p>
                            </div>
                            <div>
                                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                    <button
                                        onClick={() => updatePage(Math.max(1, currentPage - 1))}
                                        disabled={currentPage <= 1}
                                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        <span className="sr-only">Previous</span>
                                        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </button>

                                    {/* Page Numbers */}
                                    {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                                        let pageNum;
                                        if (pagination.totalPages <= 5) {
                                            pageNum = i + 1;
                                        } else if (currentPage <= 3) {
                                            pageNum = i + 1;
                                        } else if (currentPage >= pagination.totalPages - 2) {
                                            pageNum = pagination.totalPages - 4 + i;
                                        } else {
                                            pageNum = currentPage - 2 + i;
                                        }

                                        return (
                                            <button
                                                key={pageNum}
                                                onClick={() => updatePage(pageNum)}
                                                className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${pageNum === currentPage
                                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                                    }`}
                                            >
                                                {pageNum}
                                            </button>
                                        );
                                    })}

                                    <button
                                        onClick={() => updatePage(Math.min(pagination.totalPages, currentPage + 1))}
                                        disabled={currentPage >= pagination.totalPages}
                                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        <span className="sr-only">Next</span>
                                        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </button>
                                </nav>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </AdminGuard>
    );
};

export default CartsPage; 