"use client";

import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import Modal from 'react-modal';

import { AdminGuard } from '../../../components/common/AdminGuard';
import StatusBadge from '../../components/StatusBadge';
import MapSelector from '../../../components/MapSelector';
import {
    getCartByIdForAdmin,
    syncAdminCartChanges,
    processAdminCartCheckout,
    calculateAdminCartTotal,
    formatCartCreatedDate,
    validateAdminCartItems,
} from '../../../services/cartService';
import { getSkuById } from '../../../services/skuService';
import { AdminCart } from '../../../types/session';
import { SKU } from '../../../types/sku';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { formatIndianMobile } from '@/lib/utils';
import { getClosestFacility } from '@/lib/polygonUtils';
import SkuSelectionModal from '@/app/components/SkuSelectionModal';
import { logger } from '@/lib/logger';

// Cart item interface for editing
interface CartItemEdit {
    skuId: number;
    variantSkuId?: number;
    quantity: number;
    sellingPrice: number;
    costPrice: number;
    mrp: number;
    skuName: string;
    skuImage: string;
}

// Edit data interface
interface CartEditData {
    customerName: string;
    customerPhone: string;
    location: { lat: number; lng: number };
    cartItems: CartItemEdit[];
}

// Utility function to safely render values and prevent object rendering errors
const safeRender = (value: unknown): string => {
    if (value === null || value === undefined) {
        return '';
    }
    if (typeof value === 'object') {
        console.warn('Attempted to render object directly:', value);
        return JSON.stringify(value);
    }
    return String(value);
};

const CartDetailPage: React.FC = () => {
    const { t } = useTranslation();
    const params = useParams();
    const router = useRouter();
    const searchParams = useSearchParams();
    const queryClient = useQueryClient();
    const cartId = params.cartId as string;

    const [isEditing, setIsEditing] = useState(false);
    const [editData, setEditData] = useState<CartEditData>({
        customerName: '',
        customerPhone: '',
        location: { lat: 0, lng: 0 },
        cartItems: []
    });
    const [skuDetails, setSkuDetails] = useState<Record<number, SKU>>({});
    const [invalidItems, setInvalidItems] = useState<Array<{ skuId: number; variantSkuId?: number; quantity: number; skuName?: string; reason: string }>>([]);

    // SKU Modal states
    const [showSkuModal, setShowSkuModal] = useState(false);

    // Checkout Modal states
    const [showCheckoutModal, setShowCheckoutModal] = useState(false);

    // Fetch cart data
    const {
        data: cart,
        isLoading,
        isError,
        error,
        refetch
    } = useQuery<AdminCart, Error>({
        queryKey: ['admin-cart', cartId],
        queryFn: () => getCartByIdForAdmin(cartId),
        enabled: !!cartId,
        staleTime: 30000,
        gcTime: 300000,
    });



    // Update cart mutation
    const updateCartMutation = useMutation({
        mutationFn: (updateData: CartEditData) => syncAdminCartChanges(cartId, {
            customerName: updateData.customerName,
            customerPhone: updateData.customerPhone,
            location: updateData.location,
            cartItems: updateData.cartItems.map(item => ({
                skuId: item.skuId,
                variantSkuId: item.variantSkuId,
                quantity: item.quantity
            }))
        }),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['admin-cart', cartId] });
            queryClient.invalidateQueries({ queryKey: ['admin-carts'] });
            setIsEditing(false);
            toast.success(t('admin.cart.detail.updateSuccess'), {
                position: "top-right",
                autoClose: 3000,
            });
        },
        onError: (error: Error) => {
            toast.error(error.message || t('admin.cart.detail.updateError'), {
                position: "top-right",
                autoClose: 5000,
            });
        }
    });

    // Checkout cart mutation
    const checkoutCartMutation = useMutation({
        mutationFn: (checkoutData: CartEditData) => processAdminCartCheckout(cartId, {
            customerName: checkoutData.customerName,
            customerPhone: checkoutData.customerPhone,
            location: checkoutData.location,
            cartItems: checkoutData.cartItems.map(item => ({
                skuId: item.skuId,
                variantSkuId: item.variantSkuId,
                quantity: item.quantity
            }))
        }),
        onSuccess: (response) => {
            queryClient.invalidateQueries({ queryKey: ['admin-cart', cartId] });
            queryClient.invalidateQueries({ queryKey: ['admin-carts'] });
            setShowCheckoutModal(false);
            toast.success(response.message || t('admin.cart.detail.checkoutSuccess'), {
                position: "top-right",
                autoClose: 3000,
            });
        },
        onError: (error: Error) => {
            toast.error(error.message || t('admin.cart.detail.checkoutError'), {
                position: "top-right",
                autoClose: 5000,
            });
        }
    });

    // Initialize edit data when entering edit mode
    const handleEditToggle = async () => {
        if (!isEditing && cart) {
            // Convert cart data to edit format
            const cartItems: CartItemEdit[] = cart.cartJson.skuItems.map(item => ({
                skuId: item.skuId,
                variantSkuId: undefined, // Admin carts don't use variants in this structure
                quantity: item.quantity,
                sellingPrice: item.sellingPrice,
                costPrice: item.costPrice,
                mrp: item.mrp,
                skuName: item.skuName || '',
                skuImage: item.skuImage || ''
            }));

            // Parse location from string format
            const locationParts = cart.cartJson.customer.location;
            const lat = parseFloat(locationParts.lat) || 0;
            const lng = parseFloat(locationParts.lng) || 0;

            setEditData({
                customerName: cart.cartJson.customer.name,
                customerPhone: cart.cartJson.customer.mobile,
                location: { lat, lng },
                cartItems
            });

            // Validate cart items
            await validateCartItems(cartItems);
        }
        setIsEditing(!isEditing);
    };

    const handleSave = async () => {
        if (!cart || !editData) return;
        updateCartMutation.mutate(editData);
    };

    const handleCancel = () => {
        setIsEditing(false);
        setEditData({
            customerName: '',
            customerPhone: '',
            location: { lat: 0, lng: 0 },
            cartItems: []
        });
        setInvalidItems([]);
    };

    const handleFieldChange = useCallback((field: keyof CartEditData, value: unknown) => {
        setEditData(prev => ({ ...prev, [field]: value }));
    }, []);

    const handleCartItemChange = (index: number, field: keyof CartItemEdit, value: unknown) => {
        setEditData(prev => ({
            ...prev,
            cartItems: prev.cartItems.map((item, i) =>
                i === index ? { ...item, [field]: value } : item
            )
        }));
    };

    const handleAddSkuItem = () => {
        setShowSkuModal(true);
    };

    const handleSkuModalSelection = (selectedSkus: SKU[]) => {
        // Use real SKU data with actual pricing information
        const newItems: CartItemEdit[] = selectedSkus.map(sku => ({
            skuId: sku.skuId,
            variantSkuId: undefined,
            quantity: 1,
            sellingPrice: sku.sellingPrice || 0,
            costPrice: sku.costPrice || 0,
            mrp: sku.mrp || 0,
            skuName: sku.name.en,
            skuImage: sku.imageUrl || ''
        }));

        setEditData(prev => ({
            ...prev,
            cartItems: [...prev.cartItems, ...newItems]
        }));

        // Store SKU details in the details cache for immediate display
        const skuDetailsToAdd = selectedSkus.reduce((acc, sku) => {
            acc[sku.skuId] = sku;
            return acc;
        }, {} as Record<number, SKU>);

        setSkuDetails(prev => ({ ...prev, ...skuDetailsToAdd }));

        setShowSkuModal(false);
    };

    const handleRemoveSkuItem = (index: number) => {
        setEditData(prev => ({
            ...prev,
            cartItems: prev.cartItems.filter((_, i) => i !== index)
        }));
    };

    const handleCheckout = () => {
        setShowCheckoutModal(true);
    };

    const handleConfirmCheckout = () => {
        if (!editData) return;
        checkoutCartMutation.mutate(editData);
    };

    // Parse delivery location string into coordinates
    const deliveryCoords = useMemo(() => {
        if (isEditing) {
            return editData.location;
        }

        if (!cart?.cartJson.customer.location) return null;

        try {
            const lat = parseFloat(cart.cartJson.customer.location.lat);
            const lng = parseFloat(cart.cartJson.customer.location.lng);
            if (!isNaN(lat) && !isNaN(lng)) {
                return { lat, lng };
            }
        } catch (error) {
            console.warn('Error parsing cart location:', error);
        }
        return null;
    }, [isEditing, editData.location, cart?.cartJson.customer.location]);

    // Handle location selection from map
    const handleLocationSelect = useCallback((location: { lat: number; lng: number }) => {
        setEditData(prev => ({
            ...prev,
            location: location
        }));
    }, []);

    // Facility detection (same as order detail)
    const facilityInPolygon = useMemo(() => {
        const currentLocation = deliveryCoords;
        if (currentLocation) {
            return getClosestFacility(currentLocation.lat, currentLocation.lng);
        }
        return null;
    }, [deliveryCoords]);

    // Function to fetch SKU details for missing SKUs
    const fetchMissingSkuDetails = useCallback(async (skuIds: number[]) => {
        const validSkuIds = skuIds.filter(skuId => skuId && skuId > 0);
        const missingSkuIds = validSkuIds.filter(skuId => !skuDetails[skuId]);

        if (missingSkuIds.length === 0) return;



        for (const skuId of missingSkuIds) {
            try {
                const skuDetail = await getSkuById(skuId);
                if (skuDetail) {
                    setSkuDetails(prev => ({ ...prev, [skuId]: skuDetail }));

                } else {
                    console.warn(`CartDetail: No SKU details found for ID ${skuId}`);
                }
            } catch (error) {
                console.error(`Failed to fetch SKU details for ID ${skuId}:`, error);
            }
        }
    }, [skuDetails]);

    // Validate cart items
    const validateCartItems = useCallback(async (cartItems: CartItemEdit[]) => {
        try {
            const validation = await validateAdminCartItems(cartItems.map(item => ({
                skuId: item.skuId,
                variantSkuId: item.variantSkuId,
                quantity: item.quantity,
                skuName: item.skuName
            })));

            setInvalidItems(validation.invalidItems);

            // Update SKU details cache with valid items
            const skuDetailsToAdd = validation.validItems.reduce((acc, item) => {
                acc[item.skuId] = item.sku;
                return acc;
            }, {} as Record<number, SKU>);

            setSkuDetails(prev => ({ ...prev, ...skuDetailsToAdd }));
        } catch (error) {
            console.error('Error validating cart items:', error);
        }
    }, []);

    // Fetch SKU details for initial cart items
    useEffect(() => {
        if (cart?.cartJson.skuItems && cart.cartJson.skuItems.length > 0) {
            const skuIds = cart.cartJson.skuItems.map(item => item.skuId);
            fetchMissingSkuDetails(skuIds);
        }
    }, [cart?.cartJson.skuItems, fetchMissingSkuDetails]);

    // Fetch SKU details for edit data items when they change
    useEffect(() => {
        if (isEditing && editData.cartItems && editData.cartItems.length > 0) {
            const skuIds = editData.cartItems.map(item => item.skuId);
            fetchMissingSkuDetails(skuIds);
        }
    }, [editData.cartItems, isEditing, fetchMissingSkuDetails]);

    if (!cartId) {
        return (
            <AdminGuard requiredPermission="viewAllCarts">
                <div className="p-6">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 className="text-lg font-medium text-red-800">
                            {t('admin.cart.detail.invalidId')}
                        </h3>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    if (isLoading) {
        return (
            <AdminGuard requiredPermission="viewAllCarts">
                <div className="p-6">
                    <div className="animate-pulse space-y-6">
                        <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                        <div className="bg-white p-6 rounded-lg shadow space-y-4">
                            <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        </div>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    if (isError || !cart) {
        return (
            <AdminGuard requiredPermission="viewAllCarts">
                <div className="p-6">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 className="text-lg font-medium text-red-800">
                            {t('admin.cart.detail.notFound')}
                        </h3>
                        <p className="text-red-600 mt-2">
                            {error?.message || t('admin.cart.detail.loadError')}
                        </p>
                        <button
                            onClick={() => router.push('/admin/cart')}
                            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                        >
                            {t('admin.cart.detail.backToCarts')}
                        </button>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    const currentCartItems = isEditing ? editData.cartItems : cart.cartJson.skuItems;
    const currentCustomer = isEditing ? {
        name: editData.customerName,
        phone: editData.customerPhone,
        location: editData.location
    } : {
        name: cart.cartJson.customer.name,
        phone: cart.cartJson.customer.mobile,
        location: deliveryCoords
    };

    const cartTotal = isEditing
        ? editData.cartItems.reduce((total, item) => total + (item.sellingPrice * item.quantity), 0)
        : calculateAdminCartTotal(cart);

    return (
        <AdminGuard requiredPermission="viewAllCarts">
            <div className="p-6 max-w-7xl mx-auto">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                            {t('admin.cart.detail.title')} #{cart.cartId}
                        </h1>
                        <p className="text-gray-600 mt-1">
                            {t('admin.cart.detail.subtitle')}
                        </p>
                    </div>
                    <div className="flex space-x-3">
                        <button
                            onClick={() => {
                                // Check if we have returnFilters to preserve filter state
                                const returnFilters = searchParams.get('returnFilters');
                                if (returnFilters) {
                                    const decodedFilters = decodeURIComponent(returnFilters);
                                    logger.debug('Returning to carts with filters', { decodedFilters });
                                    router.push(`/admin/cart?${decodedFilters}`);
                                } else {
                                    router.push('/admin/cart');
                                }
                            }}
                            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                        >
                            {t('admin.cart.detail.backToCarts')}
                        </button>
                        <AdminGuard requiredPermission="editCustomerCart">
                            {isEditing ? (
                                <>
                                    <button
                                        onClick={handleCancel}
                                        className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                                    >
                                        {t('common.cancel')}
                                    </button>
                                    <button
                                        onClick={handleSave}
                                        disabled={updateCartMutation.isPending}
                                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                                    >
                                        {updateCartMutation.isPending ? t('common.saving') : t('common.save')}
                                    </button>
                                </>
                            ) : (
                                <button
                                    onClick={handleEditToggle}
                                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                                >
                                    {t('common.edit')}
                                </button>
                            )}
                        </AdminGuard>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Cart Overview */}
                        <div className="bg-white p-6 rounded-lg shadow">
                            <h2 className="text-lg font-medium text-gray-900 mb-4">
                                {t('admin.cart.detail.overview')}
                            </h2>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div>
                                    <p className="text-sm text-gray-600">{t('admin.cart.detail.status')}</p>
                                    <StatusBadge status={cart.status} />
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">{t('admin.cart.detail.created')}</p>
                                    <p className="font-medium">{formatCartCreatedDate(cart.rowCreatedAt)}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">{t('admin.cart.detail.total')}</p>
                                    <p className="font-medium text-green-600">₹{cartTotal.toFixed(2)}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">{t('admin.cart.detail.items')}</p>
                                    <p className="font-medium">{currentCartItems.length}</p>
                                </div>
                            </div>
                        </div>

                        {/* Customer Information */}
                        <div className="bg-white p-6 rounded-lg shadow">
                            <h2 className="text-lg font-medium text-gray-900 mb-4">
                                {t('admin.cart.detail.customerInfo')}
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        {t('admin.cart.detail.customerName')}
                                    </label>
                                    {isEditing ? (
                                        <input
                                            type="text"
                                            value={editData.customerName}
                                            onChange={(e) => handleFieldChange('customerName', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    ) : (
                                        <p className="text-gray-900">{safeRender(currentCustomer.name)}</p>
                                    )}
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        {t('admin.cart.detail.customerPhone')}
                                    </label>
                                    {isEditing ? (
                                        <input
                                            type="text"
                                            value={editData.customerPhone}
                                            onChange={(e) => handleFieldChange('customerPhone', formatIndianMobile(e.target.value))}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="+91"
                                        />
                                    ) : (
                                        <p className="text-gray-900">{(safeRender(currentCustomer.phone))}</p>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Delivery Location */}
                        <div className="bg-white p-6 rounded-lg shadow">
                            <h2 className="text-lg font-medium text-gray-900 mb-4">
                                {t('admin.cart.detail.locationTitle')}
                            </h2>
                            <div className="grid grid-cols-1 gap-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        {t('admin.cart.detail.coordinates')}
                                    </label>
                                    {isEditing ? (
                                        <input
                                            type="text"
                                            value={`${editData.location.lat} ${editData.location.lng}`}
                                            onChange={(e) => {
                                                const parts = e.target.value.split(/\s+/);
                                                if (parts.length === 2) {
                                                    const lat = parseFloat(parts[0].trim());
                                                    const lng = parseFloat(parts[1].trim());
                                                    if (!isNaN(lat) && !isNaN(lng)) {
                                                        handleFieldChange('location', { lat, lng });
                                                    }
                                                }
                                            }}
                                            placeholder={t('admin.cart.detail.coordinatesPlaceholder')}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    ) : (
                                        <p className="text-gray-900 px-3 py-2 border border-gray-200 rounded-md bg-gray-50">
                                            {currentCustomer.location ? `${currentCustomer.location.lat.toFixed(6)} ${currentCustomer.location.lng.toFixed(6)}` : t('admin.cart.detail.noLocation')}
                                        </p>
                                    )}
                                </div>

                                {/* Serviceability Status */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        {t('admin.cart.detail.serviceability')}
                                    </label>
                                    {facilityInPolygon ? (
                                        <div className="flex items-center px-3 py-2 border border-green-200 rounded-md bg-green-50">
                                            <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <span className="text-sm font-medium text-green-800">
                                                {t('admin.cart.detail.serviceableWith', { facility: facilityInPolygon })}
                                            </span>
                                        </div>
                                    ) : (
                                        <div className="flex items-center px-3 py-2 border border-red-200 rounded-md bg-red-50">
                                            <svg className="h-5 w-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.966-.833-2.736 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                                            </svg>
                                            <span className="text-sm font-medium text-red-800">
                                                {t('admin.cart.detail.notServiceable')}
                                            </span>
                                        </div>
                                    )}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        {t('admin.cart.detail.map')}
                                    </label>
                                    <MapSelector
                                        initialLocation={deliveryCoords || undefined}
                                        onLocationSelect={handleLocationSelect}
                                        disabled={!isEditing}
                                        showVillagePolygons={true}
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Cart Items */}
                        <div className="bg-white rounded-lg shadow">
                            <div className="p-6 border-b border-gray-200">
                                <div className="flex items-center justify-between">
                                    <h2 className="text-lg font-medium text-gray-900">
                                        {t('admin.cart.detail.cartItems')}
                                    </h2>
                                    {isEditing && (
                                        <AdminGuard requiredPermission="editCustomerCart">
                                            <button
                                                onClick={handleAddSkuItem}
                                                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                                            >
                                                {t('admin.cart.detail.addItem')}
                                            </button>
                                        </AdminGuard>
                                    )}
                                </div>
                            </div>

                            {/* Invalid Items Warning */}
                            {invalidItems.length > 0 && (
                                <div className="p-4 bg-yellow-50 border-b border-yellow-200">
                                    <h3 className="text-sm font-medium text-yellow-800 mb-2">
                                        {t('admin.cart.detail.invalidItems')}
                                    </h3>
                                    <ul className="text-sm text-yellow-700 space-y-1">
                                        {invalidItems.map((item, index) => (
                                            <li key={index}>
                                                SKU #{item.skuId} ({item.skuName || 'Unknown'}): {item.reason}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}

                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('admin.cart.detail.product')}
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('admin.cart.detail.price')}
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('admin.cart.detail.quantity')}
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('admin.cart.detail.total')}
                                            </th>
                                            {isEditing && (
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('common.actions')}
                                                </th>
                                            )}
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {currentCartItems.map((item, index) => {
                                            const sku = skuDetails[item.skuId];
                                            const isInvalid = invalidItems.some(invalid => invalid.skuId === item.skuId);

                                            return (
                                                <tr key={`${item.skuId}-${index}`} className={isInvalid ? 'bg-red-50' : 'hover:bg-gray-50'}>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex items-center">
                                                            <div className="h-10 w-10 flex-shrink-0">
                                                                <Image
                                                                    src={item.skuImage || '/placeholder-product.png'}
                                                                    alt={item.skuName || 'Product'}
                                                                    width={40}
                                                                    height={40}
                                                                    className="h-10 w-10 rounded object-cover"
                                                                />
                                                            </div>
                                                            <div className="ml-4">
                                                                <div className="text-sm font-medium text-gray-900">
                                                                    {item.skuName}
                                                                </div>
                                                                <div className="text-sm text-gray-500">
                                                                    SKU #{item.skuId}
                                                                </div>
                                                                {isInvalid && (
                                                                    <div className="text-xs text-red-600 mt-1">
                                                                        {invalidItems.find(invalid => invalid.skuId === item.skuId)?.reason}
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">₹{item.sellingPrice}</div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        {isEditing ? (
                                                            <input
                                                                type="number"
                                                                min="1"
                                                                value={item.quantity}
                                                                onChange={(e) => handleCartItemChange(index, 'quantity', parseInt(e.target.value) || 1)}
                                                                className="w-20 px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                            />
                                                        ) : (
                                                            <div className="text-sm text-gray-900">{item.quantity}</div>
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">₹{(item.sellingPrice * item.quantity).toFixed(2)}</div>
                                                    </td>
                                                    {isEditing && (
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <button
                                                                onClick={() => handleRemoveSkuItem(index)}
                                                                className="text-red-600 hover:text-red-800"
                                                            >
                                                                {t('common.remove')}
                                                            </button>
                                                        </td>
                                                    )}
                                                </tr>
                                            );
                                        })}
                                    </tbody>
                                </table>

                                {currentCartItems.length === 0 && (
                                    <div className="text-center py-8">
                                        <p className="text-gray-500">{t('admin.cart.detail.emptyCart')}</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Quick Actions */}
                        <div className="bg-white p-6 rounded-lg shadow">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">
                                {t('admin.cart.detail.quickActions')}
                            </h3>
                            <div className="space-y-3">
                                {cart.status === 'CREATED' && (
                                    <AdminGuard requiredPermission="editCustomerCart">
                                        <button
                                            onClick={handleCheckout}
                                            disabled={currentCartItems.length === 0 || invalidItems.length > 0}
                                            className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                        >
                                            {t('admin.cart.detail.processCheckout')}
                                        </button>
                                    </AdminGuard>
                                )}
                                <button
                                    onClick={() => refetch()}
                                    className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                                >
                                    {t('common.refresh')}
                                </button>
                            </div>
                        </div>

                        {/* Cart Summary */}
                        <div className="bg-white p-6 rounded-lg shadow">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">
                                {t('admin.cart.detail.summary')}
                            </h3>
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <span className="text-gray-600">{t('admin.cart.detail.itemCount')}</span>
                                    <span className="font-medium">{currentCartItems.length}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">{t('admin.cart.detail.totalQuantity')}</span>
                                    <span className="font-medium">{currentCartItems.reduce((sum, item) => sum + item.quantity, 0)}</span>
                                </div>
                                <div className="border-t pt-2">
                                    <div className="flex justify-between">
                                        <span className="text-lg font-medium text-gray-900">{t('admin.cart.detail.cartTotal')}</span>
                                        <span className="text-lg font-bold text-green-600">₹{cartTotal.toFixed(2)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* SKU Selection Modal */}
                <SkuSelectionModal
                    isOpen={showSkuModal}
                    onClose={() => setShowSkuModal(false)}
                    onSelect={handleSkuModalSelection}
                    title={t('admin.cart.detail.addItems')}
                    searchPlaceholder={t('admin.cart.detail.searchSkus')}
                    filterType="all"
                    activeOnly={true}
                    excludeSkuIds={editData.cartItems.map(item => item.skuId)}
                />

                {/* Checkout Confirmation Modal */}
                <Modal
                    isOpen={showCheckoutModal}
                    onRequestClose={() => setShowCheckoutModal(false)}
                    contentLabel={t('admin.cart.detail.confirmCheckout')}
                    className="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-md bg-white focus:outline-none"
                    shouldCloseOnOverlayClick={true}
                    shouldCloseOnEsc={true}
                >
                    <div className="text-center">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            {t('admin.cart.detail.confirmCheckout')}
                        </h3>
                        <p className="text-gray-600 mb-6">
                            {t('admin.cart.detail.checkoutWarning')}
                        </p>
                        <div className="flex space-x-3 justify-center">
                            <button
                                onClick={() => setShowCheckoutModal(false)}
                                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                            >
                                {t('common.cancel')}
                            </button>
                            <button
                                onClick={handleConfirmCheckout}
                                disabled={checkoutCartMutation.isPending}
                                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                            >
                                {checkoutCartMutation.isPending ? t('common.processing') : t('admin.cart.detail.confirmCheckout')}
                            </button>
                        </div>
                    </div>
                </Modal>
            </div>
        </AdminGuard>
    );
};

export default CartDetailPage; 