export interface OrderItem {
    unit: string;
    price: number;
    skuID: number;
    skuName: string;
    skuImage: string;
    quantity: number;
    pickedQuantity?: number; // Optional - defaults to quantity if not provided
    deliveredQuantity?: number; // Optional -
}

export interface OrderMetadata {
    orderComments?: string;
    deliveryAddress?: string;
    reason?: string; // cancellation reason
    deliveryInstruction?: string;
    cancelledAt?: string; // ISO date string when order was cancelled
    confirmedAt?: string; // ISO date string when order was confirmed
    returnData?: {
        skus: Array<{
            skuId: number;
            expectedQuantity: number;
            quantity: number;
            unit: string;
            inventoryLoss: number; // calculated as expectedQuantity - quantity
        }>;
    };
}

export interface Order {
    id: number;
    type: "INFINITY";
    status: OrderStatus;
    metadata: OrderMetadata;
    mobile: string | null;
    facilityKey: string;
    deliveryLocation: string; // coordinates as string
    skuItems: OrderItem[];
    deliveryDate: string; // ISO date string
    fullfillingOpsRosterId: number;
    orderId: string | null;
    createdAt: number | null; // Epoch seconds (Unix timestamp)
    createdBy: number | null; // User ID who created the order
    endCustomerName?: string; // Name of the end customer
}

export interface GetOrdersForAdminRequest {
    dateFrom: number; // Epoch seconds (Unix timestamp)
    dateTo: number; // Epoch seconds (Unix timestamp)
}

export interface GetOrdersForAdminResponse {
    status: boolean;
    data: {
        orders: Order[];
    };
}

export interface OrderFilters {
    dateFrom: string;
    dateTo: string;
    status?: string;
    facilityKey?: string;
}

export interface UpdateOrderRequest {
    id: number;
    status?: OrderStatus;
    mobile?: string | null;
    facilityKey?: string;
    deliveryLocation?: string;
    deliveryDate?: string;
    skuItems?: OrderItem[];
    metadata?: OrderMetadata;
}

export interface GetOrderByIdRequest {
    id: number;
}

export interface GetOrderByIdResponse {
    order: Record<string, unknown>;
    message?: string;
}

// Helper type for order status options
export const ORDER_STATUSES = [
    "NEW",
    "CONFIRMED",
    "PICKED",
    "CANCELLED",
    "SCHEDULED",
    "DELIVERED",
    "PARTIALLY_DELIVERED",
    "UNDELIVERED",
    "READY_TO_SHIP"
] as const;

export type OrderStatus = typeof ORDER_STATUSES[number];