import { useSearch<PERSON>ara<PERSON>, useRouter, usePathname } from 'next/navigation';
import { useCallback, useMemo } from 'react';
import { LeadFilters } from '../types/lead';
import { getDefaultLeadDateRange } from '../services/leadUtils';
import { logger } from '@/lib/logger';

interface UseLeadFiltersReturn {
    filters: LeadFilters;
    currentPage: number;
    limit: number;
    updateFilter: (key: keyof LeadFilters, value: string | number) => void;
    updatePage: (page: number) => void;
    resetFilters: () => void;
    clearValueFilters: () => void;
    getServerFilters: () => {
        leadSource?: string;
        name?: string;
        type?: string;
        status?: string;
        endCustomerMobile?: string;
    };
    getClientFilters: () => {
        search: string;
        isNameSearch: boolean;
        isMobileSearch: boolean;
        dateFrom: string;
        dateTo: string;
    };
}

/**
 * Convert YYYY-MM-DD to dd-MM-yyyy for URL display
 */
const formatDateForURL = (apiDate: string): string => {
    if (!apiDate) return '';
    const [year, month, day] = apiDate.split('-');
    return `${day}-${month}-${year}`;
};

/**
 * Convert dd-MM-yyyy from URL to YYYY-MM-DD for API
 * Also handles YYYY-MM-DD format for backward compatibility
 */
const parseDateFromURL = (urlDate: string): string => {
    if (!urlDate) return '';

    // If already in YYYY-MM-DD format, return as-is
    if (urlDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return urlDate;
    }

    // Convert dd-MM-yyyy to YYYY-MM-DD
    if (urlDate.match(/^\d{2}-\d{2}-\d{4}$/)) {
        const [day, month, year] = urlDate.split('-');
        return `${year}-${month}-${day}`;
    }

    return '';
};

/**
 * Hook to manage lead filters with URL query parameter synchronization
 * URL parameters take priority over default values
 */
export const useLeadFilters = (): UseLeadFiltersReturn => {
    const searchParams = useSearchParams();
    const router = useRouter();
    const pathname = usePathname();

    // Get default values
    const defaultDateRange = useMemo(() => getDefaultLeadDateRange(), []);
    const defaultLimit = 50;

    // Parse filters from URL with fallbacks to defaults
    const filters = useMemo((): LeadFilters => {
        // URL parameters take priority, fallback to defaults
        const dateFromURL = searchParams.get('dateFrom');
        const dateToURL = searchParams.get('dateTo');

        const dateFrom = dateFromURL ? parseDateFromURL(dateFromURL) : defaultDateRange.dateFrom;
        const dateTo = dateToURL ? parseDateFromURL(dateToURL) : defaultDateRange.dateTo;
        const leadSource = searchParams.get('leadSource') || '';
        const type = searchParams.get('type') || '';
        const status = searchParams.get('status') || '';
        const search = searchParams.get('search') || '';

        logger.debug('LeadFilters: Parsed from URL', {
            dateFrom,
            dateTo,
            leadSource,
            type,
            status,
            search,
            fromURL: {
                dateFrom: searchParams.get('dateFrom'),
                dateTo: searchParams.get('dateTo'),
                leadSource: searchParams.get('leadSource'),
                type: searchParams.get('type'),
                status: searchParams.get('status'),
                search: searchParams.get('search')
            }
        });

        return {
            dateFrom,
            dateTo,
            leadSource,
            type,
            status,
            search
        };
    }, [searchParams, defaultDateRange]);

    // Parse pagination from URL
    const currentPage = useMemo(() => {
        const pageParam = searchParams.get('page');
        return pageParam ? Math.max(1, parseInt(pageParam)) : 1;
    }, [searchParams]);

    const limit = useMemo(() => {
        const limitParam = searchParams.get('limit');
        return limitParam ? Math.max(1, parseInt(limitParam)) : defaultLimit;
    }, [searchParams, defaultLimit]);

    /**
     * Update URL with new filter values and pagination
     */
    const updateURL = useCallback((newFilters: LeadFilters, newPage?: number, newLimit?: number) => {
        const params = new URLSearchParams();

        // Add filters - only non-empty values to keep URL clean
        if (newFilters.dateFrom) params.set('dateFrom', formatDateForURL(newFilters.dateFrom));
        if (newFilters.dateTo) params.set('dateTo', formatDateForURL(newFilters.dateTo));
        if (newFilters.leadSource) params.set('leadSource', newFilters.leadSource);
        if (newFilters.type) params.set('type', newFilters.type);
        if (newFilters.status) params.set('status', newFilters.status);
        if (newFilters.search) params.set('search', newFilters.search);

        // Add pagination
        const finalPage = newPage ?? currentPage;
        const finalLimit = newLimit ?? limit;
        if (finalPage > 1) params.set('page', finalPage.toString());
        if (finalLimit !== defaultLimit) params.set('limit', finalLimit.toString());

        const newURL = `${pathname}?${params.toString()}`;

        logger.debug('LeadFilters: Updating URL', { newFilters, newPage, newLimit, newURL });

        // Use replace to avoid polluting browser history
        router.replace(newURL);
    }, [pathname, router, currentPage, limit, defaultLimit]);

    /**
     * Update a single filter field
     */
    const updateFilter = useCallback((key: keyof LeadFilters, value: string | number) => {
        const newFilters = { ...filters, [key]: value };

        // Reset to page 1 when filters change
        updateURL(newFilters, 1);
    }, [filters, updateURL]);

    /**
     * Update pagination without changing filters
     */
    const updatePage = useCallback((page: number) => {
        updateURL(filters, page);
    }, [filters, updateURL]);

    /**
     * Reset all filters to defaults
     */
    const resetFilters = useCallback(() => {
        const defaultFilters: LeadFilters = {
            leadSource: '',
            type: '',
            status: '',
            dateFrom: defaultDateRange.dateFrom,
            dateTo: defaultDateRange.dateTo,
            search: ''
        };

        updateURL(defaultFilters, 1);
    }, [defaultDateRange, updateURL]);

    /**
     * Clear only value-based filters (leadSource, type, status, search)
     */
    const clearValueFilters = useCallback(() => {
        const clearedFilters: LeadFilters = {
            ...filters,
            leadSource: '',
            type: '',
            status: '',
            search: ''
        };

        updateURL(clearedFilters, 1);
    }, [filters, updateURL]);

    /**
     * Check if search term is a mobile number (exactly 10 digits)
     */
    const isMobileSearch = useCallback((searchTerm: string): boolean => {
        const cleanTerm = searchTerm.replace(/\D/g, ''); // Remove non-digits
        return cleanTerm.length === 10 && /^[6-9]/.test(cleanTerm); // 10 digits starting with 6-9
    }, []);

    /**
     * Get server-side filters (sent to API for backend filtering)
     */
    const getServerFilters = useCallback(() => {
        const searchTerm = filters.search.trim();

        if (!searchTerm) {
            return {
                leadSource: filters.leadSource || undefined,
                type: filters.type || undefined,
                status: filters.status || undefined,
                name: undefined,
                endCustomerMobile: undefined
            };
        }

        // If search term is a mobile number, send to backend with 91 prefix
        if (isMobileSearch(searchTerm)) {
            const cleanMobile = searchTerm.replace(/\D/g, '');
            return {
                leadSource: filters.leadSource || undefined,
                type: filters.type || undefined,
                status: filters.status || undefined,
                name: undefined,
                endCustomerMobile: `91${cleanMobile}`
            };
        }

        // Otherwise, don't send search to backend (will be handled locally)
        return {
            leadSource: filters.leadSource || undefined,
            type: filters.type || undefined,
            status: filters.status || undefined,
            name: undefined,
            endCustomerMobile: undefined
        };
    }, [filters, isMobileSearch]);

    /**
 * Get client-side filters (used for local filtering)
 */
    const getClientFilters = useCallback(() => {
        const searchTerm = filters.search.trim();

        return {
            search: searchTerm,
            isNameSearch: Boolean(searchTerm && !isMobileSearch(searchTerm)), // Only filter locally for name search
            isMobileSearch: Boolean(searchTerm && isMobileSearch(searchTerm)),
            dateFrom: filters.dateFrom,
            dateTo: filters.dateTo
        };
    }, [filters, isMobileSearch]);

    return {
        filters,
        currentPage,
        limit,
        updateFilter,
        updatePage,
        resetFilters,
        clearValueFilters,
        getServerFilters,
        getClientFilters
    };
}; 