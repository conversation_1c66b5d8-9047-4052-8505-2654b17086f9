'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { I18nextProvider } from 'react-i18next';
import i18n from '@/i18n';
import { AuthProvider } from '@/app/context/AuthContext';
import { SessionProvider } from '@/app/context/SessionContext';
import { OrderSuccessProvider, useOrderSuccess } from '@/app/context/OrderSuccessContext';
import OrderSuccessModal from '@/app/components/CartDrawer/OrderSuccessModal';
import AuthenticatedLayout from '@/app/components/Layout/AuthenticatedLayout';
import AdminAuthenticatedLayout from '@/app/components/Layout/AdminAuthenticatedLayout';
import MicrosoftClarity from '@/app/components/common/MicrosoftClarity';
import WhatsAppFloatingButton from '@/app/components/common/WhatsAppFloatingButton';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { initializeServices } from '@/app/services/serviceSetup';
import { PreferencesRepository } from '@/app/repository/PreferencesRepository';


// Required for react-query, but we need to ensure it's client-side only.
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import Modal from 'react-modal';
import { ToastContainer } from 'react-toastify';
import { logger } from '@/lib/logger';
import { PostHogProvider } from '@/app/posthog-provider';


// Separate component to use the OrderSuccess context
function OrderSuccessModalWrapper() {
    const { isOrderSuccessModalOpen, orderDetails, hideOrderSuccess } = useOrderSuccess();

    return (
        <OrderSuccessModal
            isOpen={isOrderSuccessModalOpen}
            onClose={hideOrderSuccess}
            orderDetails={orderDetails}
        />
    );
}

// Component to conditionally wrap with appropriate layout
function ConditionalLayout({ children }: { children: React.ReactNode }) {
    const pathname = usePathname();
    const isAdminRoute = pathname?.startsWith('/admin');

    // For admin routes, wrap with AdminAuthenticatedLayout
    if (isAdminRoute) {
        return <AdminAuthenticatedLayout>{children}</AdminAuthenticatedLayout>;
    }

    // For regular routes, wrap with AuthenticatedLayout
    return <AuthenticatedLayout>{children}</AuthenticatedLayout>;
}

export function ClientProviders({ children }: { children: React.ReactNode }) {
    // Initialize QueryClient - create it once per application lifecycle
    // Using useState to ensure client is only created once on the client-side
    const [queryClient] = useState(() => new QueryClient());

    // Initialize i18n on client side
    useEffect(() => {
        // This ensures i18n is initialized on the client side
        const initializeClientSide = async () => {
            if (typeof window !== 'undefined') {
                // Set app element for react-modal
                Modal.setAppElement('body');

                // Get language from storage or use default
                const preferencesRepository = PreferencesRepository.getInstance();

                // Migrate from localStorage if needed
                await preferencesRepository.migrateLanguageFromLocalStorage('preferredLanguage');

                // Load preferred language
                const savedLang = await preferencesRepository.loadPreferredLanguage();
                if (savedLang) {
                    i18n.changeLanguage(savedLang);
                }

                // Initialize services (dependency injection)
                try {
                    await initializeServices();
                } catch (error) {
                    logger.error('Failed to initialize services:', error);
                    // Don't throw - let app continue with degraded functionality
                }
            }
        };

        initializeClientSide();
    }, []);


    return (
        <I18nextProvider i18n={i18n}>
            <QueryClientProvider client={queryClient}>
                <AuthProvider>
                    <SessionProvider>
                        <PostHogProvider>
                            <OrderSuccessProvider>
                                <MicrosoftClarity />
                                <ToastContainer />
                                <WhatsAppFloatingButton />
                                <ConditionalLayout>
                                    {process.env.NODE_ENV === 'development' && (
                                        <ReactQueryDevtools initialIsOpen={false} />
                                    )}
                                    {children}
                                </ConditionalLayout>
                                <OrderSuccessModalWrapper />
                            </OrderSuccessProvider>
                        </PostHogProvider>
                    </SessionProvider>
                </AuthProvider>
            </QueryClientProvider>
        </I18nextProvider>
    );
}