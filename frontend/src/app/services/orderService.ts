import axiosInstance from '../../lib/axios';
import { GetOrdersForAdminRequest, GetOrdersForAdminResponse, Order, UpdateOrderRequest, ORDER_STATUSES, OrderMetadata, GetOrderByIdRequest, GetOrderByIdResponse, OrderFilters, OrderStatus } from '../types/order';

/**
 * Gets current date and time in IST timezone
 * @returns Date object representing current time in IST
 */
export const getCurrentISTDate = (): Date => {
    return new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
};

/**
 * Converts a date to IST timezone
 * @param date Date to convert
 * @returns Date object in IST timezone
 */
export const convertToIST = (date: Date): Date => {
    return new Date(date.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
};

/**
 * Formats a date for display in IST timezone
 * @param date Date to format
 * @param options Intl.DateTimeFormatOptions
 * @returns Formatted date string in IST
 */
export const formatDateIST = (date: Date, options?: Intl.DateTimeFormatOptions): string => {
    const defaultOptions: Intl.DateTimeFormatOptions = {
        timeZone: 'Asia/Kolkata',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };

    return date.toLocaleDateString('en-IN', { ...defaultOptions, ...options });
};

/**
 * Normalizes an order object to handle null/undefined values
 * @param order Raw order object from API
 * @returns Normalized order object with safe defaults
 */
const normalizeOrder = (order: Record<string, unknown>): Order => {
    // Validate and normalize status
    const rawStatus = order.status as string;
    const status = ORDER_STATUSES.includes(rawStatus as Order['status']) ? rawStatus as Order['status'] : 'NEW';

    // Handle mobile field - convert number to string if needed
    let mobile: string | null = null;
    if (order.mobile !== null && order.mobile !== undefined) {
        mobile = typeof order.mobile === 'number' ? order.mobile.toString() : (order.mobile as string);
    }

    // Extract cancellation reason from metadata.statusUpates if available
    let normalizedMetadata: OrderMetadata = {};
    if (order.metadata && typeof order.metadata === 'object') {
        // const metadata = order.metadata as Record<string, unknown>;
        normalizedMetadata = order.metadata as OrderMetadata;
    }

    const normalizedOrder: Order = {
        id: (order.id as number) || 0,
        type: "INFINITY",
        status,
        mobile,
        facilityKey: (order.facilityKey as string) || '',
        deliveryDate: (order.deliveryDate as string) || new Date().toISOString().split('T')[0] + 'T00:00:00.000',
        deliveryLocation: (order.deliveryLocation as string) || '',
        skuItems: Array.isArray(order.skuItems) ? order.skuItems : [],
        metadata: normalizedMetadata,
        fullfillingOpsRosterId: (order.fullfillingOpsRosterId as number) || (order.rosterId as number) || 0,
        orderId: (order.orderId as string) || null,
        createdAt: (order.createdAt as number) || null,
        createdBy: (order.createdBy as number) || null,
        endCustomerName: (order.endCustomerName as string) || undefined
    };

    return normalizedOrder;
};

/**
 * Converts date string to epoch seconds
 * @param dateString Date string in YYYY-MM-DD format
 * @returns Epoch seconds (Unix timestamp)
 */
const dateToEpochSeconds = (dateString: string, isEndOfDay: boolean = false): number => {
    console.log(`[orderService] Converting date string to epoch seconds: ${dateString}`);
    const date = new Date(dateString + (isEndOfDay ? 'T23:59:59.999' : 'T00:00:00.000'));
    return Math.floor(date.getTime() / 1000);
};

/**
 * Fetches orders for admin with date range filtering
 * @param dateFrom Start date in YYYY-MM-DD format
 * @param dateTo End date in YYYY-MM-DD format
 * @returns Promise that resolves with orders array
 */
export const getOrdersForAdmin = async (dateFrom: string, dateTo: string): Promise<Order[]> => {
    try {
        console.log(`[orderService] Fetching orders from ${dateFrom} to ${dateTo}`);

        // Convert date strings to epoch seconds for backend
        const dateFromEpoch = dateToEpochSeconds(dateFrom);
        const dateToEpoch = dateToEpochSeconds(dateTo, true);

        console.log(`[orderService] Converted to epoch: ${dateFromEpoch} to ${dateToEpoch}`);

        const requestData: GetOrdersForAdminRequest = {
            dateFrom: dateFromEpoch,
            dateTo: dateToEpoch
        };

        // The axios interceptor returns the data directly, not the full response
        const responseData = await axiosInstance.post('/userApp-pos-getIntentOrdersForAdmin', requestData) as GetOrdersForAdminResponse['data'];
        // Normalize orders to handle null/undefined values
        const normalizedOrders = responseData.orders.map((order: unknown) => normalizeOrder(order as Record<string, unknown>));

        console.log(`[orderService] Successfully fetched ${normalizedOrders.length} orders`);
        return normalizedOrders;
    } catch (error) {
        console.error('[orderService] Failed to fetch orders:', error);

        // Handle specific error cases
        if (error && typeof error === 'object' && 'message' in error) {
            throw new Error(`Failed to fetch orders: ${(error as Error).message}`);
        } else if (typeof error === 'string') {
            throw new Error(`Failed to fetch orders: ${error}`);
        } else {
            throw new Error('Failed to fetch orders. Please try again.');
        }
    }
};

/**
 * Calculates the total value of an order
 * @param order Order object
 * @returns Total value as number
 */
export const calculateOrderTotal = (order: Order): number => {
    if (!order.skuItems || !Array.isArray(order.skuItems)) {
        return 0;
    }

    return order.skuItems.reduce((total, item) => {
        const price = item.price || 0;
        const quantity = item.quantity || 0;
        return total + (price * quantity);
    }, 0);
};

/**
 * Formats order delivery date for display (date part only)
 * @param deliveryDate ISO date string
 * @returns Formatted date string
 */
export const formatDeliveryDate = (deliveryDate: string | null): string => {
    if (!deliveryDate) {
        return 'No delivery date';
    }

    try {
        const date = new Date(deliveryDate);
        if (isNaN(date.getTime())) {
            return 'Invalid date';
        }

        // Format date part only
        return date.toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    } catch (error) {
        console.error('Error formatting delivery date:', error);
        return deliveryDate || 'Invalid date';
    }
};

/**
 * Gets unique facility keys from orders array
 * @param orders Array of orders
 * @returns Array of unique facility keys
 */
export const getUniqueFacilityKeys = (orders: Order[]): string[] => {
    if (!orders || !Array.isArray(orders)) {
        return [];
    }

    const facilityKeys = orders
        .map(order => order.facilityKey)
        .filter((key): key is string => key !== null && key !== undefined && key !== '');

    return Array.from(new Set(facilityKeys)).sort();
};

/**
 * Formats date to YYYY-MM-DD format for API (date part only)
 * @param date Date object
 * @returns Formatted date string (YYYY-MM-DD)
 */
export const formatDateForAPI = (date: Date): string => {
    // Extract date part only
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

/**
 * Gets default date range (yesterday to today)
 * @returns Object with dateFrom and dateTo
 */
export const getDefaultDateRange = (): { dateFrom: string; dateTo: string } => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    return {
        dateFrom: formatDateForAPI(yesterday),
        dateTo: formatDateForAPI(today)
    };
};

/**
 * Gets today-only date range (today to today)
 * @returns Object with dateFrom and dateTo both set to today
 */
export const getTodayDateRange = (): { dateFrom: string; dateTo: string } => {
    const today = new Date();

    return {
        dateFrom: formatDateForAPI(convertToIST(today)),
        dateTo: formatDateForAPI(convertToIST(today))
    };
};

/**
 * Fetches a single order by ID
 * @param orderId Order ID to fetch
 * @returns Promise that resolves with order object
 */

export const getOrderById = async (orderId: number): Promise<Order> => {
    try {
        console.log(`[orderService] Fetching order with ID: ${orderId}`);

        const requestData: GetOrderByIdRequest = {
            id: orderId
        };

        // The axios interceptor returns just the data part of the response
        const responseData = await axiosInstance.post('/userApp-pos-getIntentOrderById', requestData) as GetOrderByIdResponse;

        if (!responseData.order) {
            throw new Error(`Order with ID ${orderId} not found in response data`);
        }

        // Normalize the order to handle null/undefined values
        const normalizedOrder = normalizeOrder(responseData.order);

        console.log(`[orderService] Successfully fetched order ${orderId}`);
        return normalizedOrder;
    } catch (error) {
        console.error(`[orderService] Failed to fetch order ${orderId}:`, error);

        if (error && typeof error === 'object' && 'message' in error) {
            throw new Error(`Failed to fetch order: ${(error as Error).message}`);
        } else if (typeof error === 'string') {
            throw new Error(`Failed to fetch order: ${error}`);
        } else {
            throw new Error('Failed to fetch order. Please try again.');
        }
    }
};

/**
 * Updates an order with new data
 * @param updateData Order update data
 * @returns Promise that resolves with updated order
 */
export const updateOrder = async (updateData: UpdateOrderRequest): Promise<Order> => {
    try {
        console.log(`[orderService] Updating order ${updateData.id}:`, updateData);

        // Call the update API - axios interceptor returns just the message
        const responseData = await axiosInstance.post('/userApp-pos-submitInfinityIntentOrdersAdmin', updateData) as { message: string };

        console.log(`[orderService] Update response: ${responseData.message}`);

        // Refetch the updated order data
        const updatedOrder = await getOrderById(updateData.id);

        console.log(`[orderService] Successfully updated order ${updateData.id}`);
        return updatedOrder;
    } catch (error) {
        console.error(`[orderService] Failed to update order ${updateData.id}:`, error);

        if (error && typeof error === 'object' && 'message' in error) {
            throw new Error(`Failed to update order: ${(error as Error).message}`);
        } else if (typeof error === 'string') {
            throw new Error(`Failed to update order: ${error}`);
        } else {
            throw new Error('Failed to update order. Please try again.');
        }
    }
};

/**
 * Parses delivery location coordinates from string
 * @param deliveryLocation Coordinates string (e.g., "12.9255601 77.6746677")
 * @returns Object with lat and lng or null if invalid
 */
export const parseDeliveryLocation = (deliveryLocation: string | null): { lat: number; lng: number } | null => {
    if (!deliveryLocation || typeof deliveryLocation !== 'string') {
        return null;
    }

    try {
        const parts = deliveryLocation.trim().split(' ');
        if (parts.length === 2) {
            const lat = parseFloat(parts[0]);
            const lng = parseFloat(parts[1]);

            if (!isNaN(lat) && !isNaN(lng)) {
                return { lat, lng };
            }
        }
        return null;
    } catch (error) {
        console.error('Error parsing delivery location:', error);
        return null;
    }
};

/**
 * Formats coordinates to delivery location string
 * @param lat Latitude
 * @param lng Longitude
 * @returns Formatted coordinates string
 */
export const formatDeliveryLocation = (lat: number, lng: number): string => {
    return `${lat} ${lng}`;
};

/**
 * Formats date for date input (date part only)
 * @param dateString ISO date string
 * @returns Formatted date string for input field (YYYY-MM-DD)
 */
export const formatDateForInput = (dateString: string | null): string => {
    if (!dateString) {
        return '';
    }

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return '';
        }

        // Extract date part only (YYYY-MM-DD)
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
    } catch (error) {
        console.error('Error formatting date for input:', error);
        return '';
    }
};

/**
 * Converts date input value to ISO string (date part only)
 * @param inputValue Value from date input (YYYY-MM-DD)
 * @returns ISO string with date part only
 */
export const convertInputDateToISO = (inputValue: string): string => {
    if (!inputValue) {
        return new Date().toISOString().split('T')[0] + 'T00:00:00.000';
    }

    try {
        // Input is in format YYYY-MM-DD
        const date = new Date(inputValue + 'T00:00:00.000');
        if (isNaN(date.getTime())) {
            return new Date().toISOString().split('T')[0] + 'T00:00:00.000';
        }

        return date.toISOString();
    } catch (error) {
        console.error('Error converting input date to ISO:', error);
        return new Date().toISOString().split('T')[0] + 'T00:00:00.000';
    }
};

/**
 * Cancels an order with a reason
 * @param id Order ID to cancel
 * @param cancellationReason Reason for cancellation
 * @returns Promise that resolves with success message
 */
export const cancelOrder = async (id: number, cancellationReason: string): Promise<string> => {
    try {
        console.log(`[orderService] Cancelling order ${id} with reason: ${cancellationReason}`);

        const requestData = {
            id: id,
            status: 'CANCELLED' as const,
            metadata: {
                reason: cancellationReason,
                cancelledAt: new Date().toISOString()
            }
        };

        // Call the cancellation endpoint
        const responseData = await axiosInstance.post('/userApp-pos-submitInfinityIntentOrdersAdmin', requestData) as { message: string };

        console.log(`[orderService] Successfully cancelled order ${id}`);
        return responseData.message || 'Order cancelled successfully';
    } catch (error) {
        console.error(`[orderService] Failed to cancel order ${id}:`, error);

        if (error && typeof error === 'object' && 'message' in error) {
            throw new Error(`Failed to cancel order: ${(error as Error).message}`);
        } else if (typeof error === 'string') {
            throw new Error(`Failed to cancel order: ${error}`);
        } else {
            throw new Error('Failed to cancel order. Please try again.');
        }
    }
};

/**
 * Confirms an order by updating its status to CONFIRMED
 * @param id Order ID to confirm
 * @returns Promise that resolves with success message
 */
export const confirmOrder = async (id: number): Promise<string> => {
    try {
        console.log(`[orderService] Confirming order ${id}`);

        const requestData = {
            id: id,
            status: 'CONFIRMED' as const,
            metadata: {
                confirmedAt: new Date().toISOString()
            }
        };

        // Call the confirmation endpoint
        const responseData = await axiosInstance.post('/userApp-pos-submitInfinityIntentOrdersAdmin', requestData) as { message: string };

        console.log(`[orderService] Successfully confirmed order ${id}`);
        return responseData.message || 'Order confirmed successfully';
    } catch (error) {
        console.error(`[orderService] Failed to confirm order ${id}:`, error);

        if (error && typeof error === 'object' && 'message' in error) {
            throw new Error(`Failed to confirm order: ${(error as Error).message}`);
        } else if (typeof error === 'string') {
            throw new Error(`Failed to confirm order: ${error}`);
        } else {
            throw new Error('Failed to confirm order. Please try again.');
        }
    }
};

/**
 * Updates order status
 * @param id Order ID to update
 * @param status New status for the order
 * @returns Promise that resolves with success message
 */
export const updateOrderStatus = async (id: number, status: OrderStatus): Promise<string> => {
    try {
        console.log(`[orderService] Updating order ${id} status to ${status}`);

        const updateData: UpdateOrderRequest = {
            id,
            status
        };

        // Call the update API - axios interceptor returns just the message
        const responseData = await axiosInstance.post('/userApp-pos-submitInfinityIntentOrdersAdmin', updateData) as { message: string };

        console.log(`[orderService] Status update response: ${responseData.message}`);
        return responseData.message;
    } catch (error) {
        console.error(`[orderService] Failed to update order ${id} status:`, error);

        if (error && typeof error === 'object' && 'message' in error) {
            throw new Error(`Failed to update order status: ${(error as Error).message}`);
        } else if (typeof error === 'string') {
            throw new Error(`Failed to update order status: ${error}`);
        } else {
            throw new Error('Failed to update order status. Please try again.');
        }
    }
};