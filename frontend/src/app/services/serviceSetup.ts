/**
 * Service Setup and Dependency Injection
 * This file handles the initialization of service dependencies
 */

import { setCategoryService } from './skuService';
import * as categoryService from './categoryService';
import { initializeStorage } from '@/lib/storage';
import { logger } from '@/lib/logger';

/**
 * Initialize all service dependencies
 * Should be called early in the application lifecycle
 */
export const initializeServices = async (): Promise<void> => {
  try {
    // Initialize storage system first
    await initializeStorage({
      name: 'infinityAppDB',
      version: 1,
      description: 'Storage for Infinity App'
    });
    logger.info('✅ Storage system initialized successfully');

    // Inject categoryService into skuService
    setCategoryService(categoryService);

    logger.info('✅ Services initialized successfully');
  } catch (error) {
    logger.error('❌ Failed to initialize services:', error);
    throw error;
  }
};

/**
 * Check if services are properly initialized
 */
export const verifyServiceInitialization = (): boolean => {
  try {
    // This will throw an error if categoryService is not injected
    setCategoryService(categoryService);
    return true;
  } catch (error) {
    console.warn('⚠️ Services not properly initialized:', error);
    return false;
  }
};