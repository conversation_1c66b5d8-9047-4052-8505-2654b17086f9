@import "tailwindcss";

:root {
  --background: #f8fafc;
  --foreground: #1e293b;
  --primary: #3b82f6;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-700: #334155;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans);
  -webkit-font-smoothing: antialiased;
}

/* Custom utility classes */
@layer utilities {

  /* Hide scrollbar but allow scrolling */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari and Opera */
  }

  /* Line clamp utilities for text truncation */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Shimmer animation for skeleton loaders */
  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }

    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .animate-shimmer {
    animation: shimmer 1.5s ease-in-out infinite;
  }
}