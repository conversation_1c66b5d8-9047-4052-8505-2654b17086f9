import { RepositoryBase } from '@/app/repository/RepositoryBase';
import { logger } from '@/lib/logger';
import type { AuthData, UserDetails, TokenMetadata } from '@/app/types/auth';

const AUTH_DATA_KEY = 'user_auth_data';
const DEFAULT_AUTH_TTL_MS = 24 * 60 * 60 * 1000; // 24 hours (fallback TTL)

export class AuthRepository extends RepositoryBase {
    private static instance: AuthRepository;

    /**
     * Get the singleton instance of AuthRepository
     * @returns The singleton AuthRepository instance
     */
    public static getInstance(): AuthRepository {
        if (!AuthRepository.instance) {
            AuthRepository.instance = new AuthRepository();
        }
        return AuthRepository.instance;
    }

    private constructor() {
        // Use a longer TTL since we handle token expiry manually
        super('auth_store', DEFAULT_AUTH_TTL_MS);
    }

    /**
     * Saves complete authentication data (user details + token metadata)
     * @param authData The authentication data to save
     */
    public async saveAuthData(authData: AuthData): Promise<void> {
        try {
            await this.setItem<AuthData>(AUTH_DATA_KEY, authData);
            logger.info('AuthRepository: Auth data saved successfully.');
        } catch (error) {
            logger.error('AuthRepository: Error saving auth data:', error);
            throw error;
        }
    }

    /**
     * Retrieves the complete authentication data
     * @returns The auth data or null if not found/expired
     */
    public async getAuthData(): Promise<AuthData | null> {
        try {
            const authData = await this.getItem<AuthData>(AUTH_DATA_KEY);

            // Additional check for token expiry (beyond TTL)
            if (authData && this.isTokenExpired(authData.tokenMetadata)) {
                logger.info('AuthRepository: Token has expired, removing auth data.');
                await this.clearAuthData();
                return null;
            }

            return authData;
        } catch (error) {
            logger.error('AuthRepository: Error retrieving auth data:', error);
            return null;
        }
    }

    /**
     * Retrieves only the user details (optimized with single auth data call)
     * @returns User details or null if not authenticated
     */
    public async getUserDetails(): Promise<UserDetails | null> {
        try {
            const authData = await this.getAuthData();
            return authData?.userDetails || null;
        } catch (error) {
            logger.error('AuthRepository: Error retrieving user details:', error);
            return null;
        }
    }

    /**
     * Retrieves only the token metadata (optimized with single auth data call)
     * @returns Token metadata or null if not authenticated
     */
    public async getTokenMetadata(): Promise<TokenMetadata | null> {
        try {
            const authData = await this.getAuthData();
            return authData?.tokenMetadata || null;
        } catch (error) {
            logger.error('AuthRepository: Error retrieving token metadata:', error);
            return null;
        }
    }

    /**
     * Gets the current JWT token if valid (optimized)
     * @returns JWT token or null if not available/expired
     */
    public async getValidToken(): Promise<string | null> {
        try {
            const authData = await this.getAuthData();
            const tokenMetadata = authData?.tokenMetadata;

            if (tokenMetadata && !this.isTokenExpired(tokenMetadata)) {
                return tokenMetadata.token;
            }
            return null;
        } catch (error) {
            logger.error('AuthRepository: Error getting valid token:', error);
            return null;
        }
    }

    /**
     * Checks if the current token needs to be refreshed (optimized)
     * @returns True if token should be refreshed
     */
    public async shouldRefreshToken(): Promise<boolean> {
        try {
            const authData = await this.getAuthData();
            const tokenMetadata = authData?.tokenMetadata;

            if (!tokenMetadata) return false;

            const now = Math.floor(Date.now() / 1000); // Current time in seconds
            return now >= tokenMetadata.refreshAfter && !this.isTokenExpired(tokenMetadata);
        } catch (error) {
            logger.error('AuthRepository: Error checking token refresh status:', error);
            return false;
        }
    }

    /**
     * Updates only the token metadata (for token refresh) - optimized
     * @param tokenMetadata New token metadata
     */
    public async updateTokenMetadata(tokenMetadata: TokenMetadata): Promise<void> {
        try {
            const authData = await this.getAuthData();
            if (authData) {
                authData.tokenMetadata = tokenMetadata;
                await this.saveAuthData(authData);
                logger.info('AuthRepository: Token metadata updated successfully.');
            } else {
                throw new Error('No existing auth data found to update token metadata');
            }
        } catch (error) {
            logger.error('AuthRepository: Error updating token metadata:', error);
            throw error;
        }
    }

    /**
     * Clears all authentication data
     */
    public async clearAuthData(): Promise<void> {
        try {
            await this.removeItem(AUTH_DATA_KEY);
            logger.info('AuthRepository: Auth data cleared successfully.');
        } catch (error) {
            logger.error('AuthRepository: Error clearing auth data:', error);
            throw error;
        }
    }

    /**
     * Checks if a token is expired
     * @param tokenMetadata Token metadata to check
     * @returns True if token is expired
     */
    private isTokenExpired(tokenMetadata: TokenMetadata): boolean {
        const now = Math.floor(Date.now() / 1000); // Current time in seconds
        return now >= tokenMetadata.expiresAt;
    }

    /**
     * Gets time until token expires (in milliseconds) - optimized
     * @returns Milliseconds until expiry, or 0 if already expired
     */
    public async getTimeUntilExpiry(): Promise<number> {
        try {
            const authData = await this.getAuthData();
            const tokenMetadata = authData?.tokenMetadata;

            if (!tokenMetadata) return 0;

            const now = Math.floor(Date.now() / 1000);
            const timeUntilExpiry = (tokenMetadata.expiresAt - now) * 1000; // Convert to milliseconds
            return Math.max(0, timeUntilExpiry);
        } catch (error) {
            logger.error('AuthRepository: Error calculating time until expiry:', error);
            return 0;
        }
    }

    /**
     * Gets time until token should be refreshed (in milliseconds) - optimized
     * @returns Milliseconds until refresh time, or 0 if refresh time has passed
     */
    public async getTimeUntilRefresh(): Promise<number> {
        try {
            const authData = await this.getAuthData();
            const tokenMetadata = authData?.tokenMetadata;

            if (!tokenMetadata) return 0;

            const now = Math.floor(Date.now() / 1000);
            const timeUntilRefresh = (tokenMetadata.refreshAfter - now) * 1000; // Convert to milliseconds
            return Math.max(0, timeUntilRefresh);
        } catch (error) {
            logger.error('AuthRepository: Error calculating time until refresh:', error);
            return 0;
        }
    }

    /**
     * Saves admin roles data to the existing auth data - optimized
     * @param adminRoles Admin roles data to save
     */
    public async saveAdminRoles(adminRoles: import('../types/auth').AdminRolesData): Promise<void> {
        try {
            const authData = await this.getAuthData();
            if (authData) {
                authData.adminRoles = adminRoles;
                await this.saveAuthData(authData);
                logger.info('AuthRepository: Admin roles saved successfully.');
            } else {
                throw new Error('No existing auth data found to save admin roles');
            }
        } catch (error) {
            logger.error('AuthRepository: Error saving admin roles:', error);
            throw error;
        }
    }

    /**
     * Retrieves admin roles data - optimized
     * @returns Admin roles data or null if not found
     */
    public async getAdminRoles(): Promise<import('../types/auth').AdminRolesData | null> {
        try {
            const authData = await this.getAuthData();
            return authData?.adminRoles || null;
        } catch (error) {
            logger.error('AuthRepository: Error retrieving admin roles:', error);
            return null;
        }
    }

    /**
     * Clears admin roles data while keeping other auth data - optimized
     */
    public async clearAdminRoles(): Promise<void> {
        try {
            const authData = await this.getAuthData();
            if (authData) {
                delete authData.adminRoles;
                await this.saveAuthData(authData);
                logger.info('AuthRepository: Admin roles cleared successfully.');
            }
        } catch (error) {
            logger.error('AuthRepository: Error clearing admin roles:', error);
            throw error;
        }
    }

    /**
     * Lightweight check if user is authenticated without fetching full data
     * @returns True if user is authenticated with valid token
     */
    public async isAuthenticated(): Promise<boolean> {
        try {
            const token = await this.getValidToken();
            return token !== null;
        } catch (error) {
            logger.error('AuthRepository: Error checking authentication status:', error);
            return false;
        }
    }

    /**
     * Gets basic auth status for performance-critical checks
     * @returns Object with basic auth status info
     */
    public async getAuthStatus(): Promise<{
        isAuthenticated: boolean;
        hasAdminRoles: boolean;
        tokenExpiresIn: number;
    }> {
        try {
            const authData = await this.getAuthData();

            if (!authData || this.isTokenExpired(authData.tokenMetadata)) {
                return {
                    isAuthenticated: false,
                    hasAdminRoles: false,
                    tokenExpiresIn: 0
                };
            }

            const now = Math.floor(Date.now() / 1000);
            const tokenExpiresIn = (authData.tokenMetadata.expiresAt - now) * 1000;

            return {
                isAuthenticated: true,
                hasAdminRoles: !!authData.adminRoles,
                tokenExpiresIn: Math.max(0, tokenExpiresIn)
            };
        } catch (error) {
            logger.error('AuthRepository: Error getting auth status:', error);
            return {
                isAuthenticated: false,
                hasAdminRoles: false,
                tokenExpiresIn: 0
            };
        }
    }

    // Legacy API methods for backward compatibility with tests

    /**
     * Set access token (legacy method for test compatibility)
     * @param token The access token to set, or null to clear
     */
    public async setAccessToken(token: string | null): Promise<void> {
        try {
            if (token === null) {
                await this.clearAuthData();
                return;
            }

            // Create minimal auth data with just the token
            const authData: AuthData = {
                userDetails: { id: 0, name: '', mobileNumber: '' },
                tokenMetadata: {
                    token,
                    expiresAt: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
                    refreshAfter: Math.floor(Date.now() / 1000) + 1800 // 30 minutes from now
                }
            };

            await this.saveAuthData(authData);
            logger.info('AuthRepository: Access token set via legacy method.');
        } catch (error) {
            logger.error('AuthRepository: Error setting access token:', error);
            throw error;
        }
    }

    /**
     * Get access token (legacy method for test compatibility)
     * @returns The access token or null if not available
     */
    public async getAccessToken(): Promise<string | null> {
        try {
            return await this.getValidToken();
        } catch (error) {
            logger.error('AuthRepository: Error getting access token:', error);
            return null;
        }
    }

    /**
     * Set refresh token (legacy method for test compatibility)
     * @param token The refresh token to set, or null to clear
     */
    public async setRefreshToken(token: string | null): Promise<void> {
        try {
            const authData = await this.getAuthData();

            if (token === null) {
                if (authData) {
                    // Remove refresh token but keep other data
                    delete (authData.tokenMetadata as any).refreshToken;
                    await this.saveAuthData(authData);
                }
                return;
            }

            if (authData) {
                // Add refresh token to existing auth data
                (authData.tokenMetadata as any).refreshToken = token;
                await this.saveAuthData(authData);
            } else {
                // Create minimal auth data with refresh token
                const newAuthData: AuthData = {
                    userDetails: { id: 0, name: '', mobileNumber: '' },
                    tokenMetadata: {
                        token: '',
                        expiresAt: Math.floor(Date.now() / 1000) + 3600,
                        refreshAfter: Math.floor(Date.now() / 1000) + 1800,
                        refreshToken: token
                    } as any
                };
                await this.saveAuthData(newAuthData);
            }

            logger.info('AuthRepository: Refresh token set via legacy method.');
        } catch (error) {
            logger.error('AuthRepository: Error setting refresh token:', error);
            throw error;
        }
    }

    /**
     * Get refresh token (legacy method for test compatibility)
     * @returns The refresh token or null if not available
     */
    public async getRefreshToken(): Promise<string | null> {
        try {
            const authData = await this.getAuthData();
            return (authData?.tokenMetadata as any)?.refreshToken || null;
        } catch (error) {
            logger.error('AuthRepository: Error getting refresh token:', error);
            return null;
        }
    }

    /**
     * Set user data (legacy method for test compatibility)
     * @param user The user data to set, or null to clear
     */
    public async setUser(user: any | null): Promise<void> {
        try {
            if (user === null) {
                const authData = await this.getAuthData();
                if (authData) {
                    authData.userDetails = { id: 0, name: '', mobileNumber: '' };
                    await this.saveAuthData(authData);
                }
                return;
            }

            const authData = await this.getAuthData();
            if (authData) {
                // Update user in existing auth data
                authData.userDetails = user;
                await this.saveAuthData(authData);
            } else {
                // Create minimal auth data with user
                const newAuthData: AuthData = {
                    userDetails: user,
                    tokenMetadata: {
                        token: '',
                        expiresAt: Math.floor(Date.now() / 1000) + 3600,
                        refreshAfter: Math.floor(Date.now() / 1000) + 1800
                    }
                };
                await this.saveAuthData(newAuthData);
            }

            logger.info('AuthRepository: User data set via legacy method.');
        } catch (error) {
            logger.error('AuthRepository: Error setting user data:', error);
            throw error;
        }
    }

    /**
     * Get user data (legacy method for test compatibility)
     * @returns The user data or null if not available
     */
    public async getUser(): Promise<any | null> {
        try {
            const userDetails = await this.getUserDetails();
            // Return null if user details are empty/default
            if (!userDetails || (!userDetails.id && !userDetails.name && !userDetails.mobileNumber)) {
                return null;
            }
            return userDetails;
        } catch (error) {
            logger.error('AuthRepository: Error getting user data:', error);
            return null;
        }
    }
}

export default AuthRepository;