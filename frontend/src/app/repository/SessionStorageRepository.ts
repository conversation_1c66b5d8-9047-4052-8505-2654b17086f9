/**
 * Repository for managing session data persistence.
 * 
 * This repository handles the storage of session data and active session ID
 * using the platform-independent storage abstraction.
 */

import { RepositoryBase } from './RepositoryBase';
import { logger } from '@/lib/logger';
import type { Session } from '@/app/types/session';

const SESSIONS_KEY = 'sessions';
const ACTIVE_SESSION_ID_KEY = 'active_session_id';

export class SessionStorageRepository extends RepositoryBase {
    private static instance: SessionStorageRepository | null = null;

    /**
     * Get the singleton instance of SessionStorageRepository
     */
    public static getInstance(): SessionStorageRepository {
        if (!SessionStorageRepository.instance) {
            SessionStorageRepository.instance = new SessionStorageRepository();
        }
        return SessionStorageRepository.instance;
    }

    private constructor() {
        // Initialize with store name 'session_store' and no TTL (persistent data)
        super('session_store');
    }

    /**
     * Save sessions array to storage
     * @param sessions Array of sessions to save
     */
    public async saveSessions(sessions: Session[]): Promise<void> {
        try {
            // Convert Date objects to ISO strings for serialization
            const serializedSessions = sessions.map(session => ({
                ...session,
                lastActive: session.lastActive.toISOString()
            }));

            await this.setItem(SESSIONS_KEY, serializedSessions);
            logger.debug(`SessionStorageRepository: Saved ${sessions.length} sessions`);
        } catch (error) {
            logger.error('SessionStorageRepository: Error saving sessions:', error);
            throw error;
        }
    }

    /**
     * Load sessions array from storage
     * @returns Array of sessions or empty array if not found
     */
    public async loadSessions(): Promise<Session[]> {
        try {
            const serializedSessions = await this.getItem<any[]>(SESSIONS_KEY);
            
            if (!serializedSessions || !Array.isArray(serializedSessions)) {
                return [];
            }

            // Convert ISO strings back to Date objects
            const sessions = serializedSessions.map(session => ({
                ...session,
                lastActive: new Date(session.lastActive)
            }));

            logger.debug(`SessionStorageRepository: Loaded ${sessions.length} sessions`);
            return sessions;
        } catch (error) {
            logger.error('SessionStorageRepository: Error loading sessions:', error);
            return [];
        }
    }

    /**
     * Save active session ID to storage
     * @param sessionId The active session ID
     */
    public async saveActiveSessionId(sessionId: string): Promise<void> {
        try {
            await this.setItem(ACTIVE_SESSION_ID_KEY, sessionId);
            logger.debug(`SessionStorageRepository: Saved active session ID: ${sessionId}`);
        } catch (error) {
            logger.error('SessionStorageRepository: Error saving active session ID:', error);
            throw error;
        }
    }

    /**
     * Load active session ID from storage
     * @returns Active session ID or empty string if not found
     */
    public async loadActiveSessionId(): Promise<string> {
        try {
            const sessionId = await this.getItem<string>(ACTIVE_SESSION_ID_KEY);
            
            if (!sessionId || sessionId === 'undefined' || sessionId === 'null') {
                return '';
            }

            logger.debug(`SessionStorageRepository: Loaded active session ID: ${sessionId}`);
            return sessionId;
        } catch (error) {
            logger.error('SessionStorageRepository: Error loading active session ID:', error);
            return '';
        }
    }

    /**
     * Clear all session data from storage
     */
    public async clearAllSessionData(): Promise<void> {
        try {
            await this.clearStore();
            logger.info('SessionStorageRepository: All session data cleared');
        } catch (error) {
            logger.error('SessionStorageRepository: Error clearing session data:', error);
            throw error;
        }
    }

    /**
     * Check if session data exists in storage
     * @returns True if session data exists
     */
    public async hasSessionData(): Promise<boolean> {
        try {
            const sessions = await this.getItem<any[]>(SESSIONS_KEY);
            const activeSessionId = await this.getItem<string>(ACTIVE_SESSION_ID_KEY);
            
            return !!(sessions || activeSessionId);
        } catch (error) {
            logger.error('SessionStorageRepository: Error checking session data existence:', error);
            return false;
        }
    }

    /**
     * Get storage health status
     * @returns True if storage is healthy
     */
    public async isStorageHealthy(): Promise<boolean> {
        try {
            return await super.isStorageHealthy();
        } catch (error) {
            logger.error('SessionStorageRepository: Error checking storage health:', error);
            return false;
        }
    }

    /**
     * Migrate data from localStorage if it exists (for backward compatibility)
     * @param localStorageSessionsKey The localStorage key for sessions
     * @param localStorageActiveSessionIdKey The localStorage key for active session ID
     */
    public async migrateFromLocalStorage(
        localStorageSessionsKey: string,
        localStorageActiveSessionIdKey: string
    ): Promise<void> {
        // Only attempt migration in browser environment
        if (typeof window === 'undefined' || !window.localStorage) {
            return;
        }

        try {
            // Check if we already have data in the new storage
            const hasExistingData = await this.hasSessionData();
            if (hasExistingData) {
                logger.debug('SessionStorageRepository: Storage already has data, skipping migration');
                return;
            }

            // Try to migrate sessions
            const localStorageSessions = localStorage.getItem(localStorageSessionsKey);
            if (localStorageSessions && localStorageSessions !== 'undefined' && localStorageSessions !== 'null') {
                try {
                    const parsedSessions = JSON.parse(localStorageSessions);
                    if (Array.isArray(parsedSessions)) {
                        const sessions = parsedSessions.map((s: any) => ({
                            ...s,
                            lastActive: new Date(s.lastActive)
                        }));
                        await this.saveSessions(sessions);
                        logger.info(`SessionStorageRepository: Migrated ${sessions.length} sessions from localStorage`);
                    }
                } catch (parseError) {
                    logger.warn('SessionStorageRepository: Failed to parse sessions from localStorage:', parseError);
                }
            }

            // Try to migrate active session ID
            const localStorageActiveSessionId = localStorage.getItem(localStorageActiveSessionIdKey);
            if (localStorageActiveSessionId && localStorageActiveSessionId !== 'undefined' && localStorageActiveSessionId !== 'null') {
                await this.saveActiveSessionId(localStorageActiveSessionId);
                logger.info('SessionStorageRepository: Migrated active session ID from localStorage');
            }

            // Clean up localStorage after successful migration
            localStorage.removeItem(localStorageSessionsKey);
            localStorage.removeItem(localStorageActiveSessionIdKey);
            logger.info('SessionStorageRepository: Cleaned up localStorage after migration');

        } catch (error) {
            logger.error('SessionStorageRepository: Error during localStorage migration:', error);
            // Don't throw - migration failure shouldn't break the app
        }
    }
}

export default SessionStorageRepository;
