/**
 * Tests for RepositoryBase with storage abstraction.
 * 
 * These tests verify that the RepositoryBase works correctly
 * with the new platform-independent storage system.
 */

// Using Jest instead of Vitest
import { RepositoryBase } from '../RepositoryBase';
import { initializeStorage, createTestStorage } from '@/lib/storage';

// Create a test repository class
class TestRepository extends RepositoryBase {
    constructor(storeName: string = 'test_repository', ttl?: number) {
        super(storeName, ttl);
    }

    // Expose protected methods for testing
    public async testGetItem<T>(key: string): Promise<T | null> {
        return this.getItem<T>(key);
    }

    public async testSetItem<T>(key: string, value: T): Promise<void> {
        return this.setItem<T>(key, value);
    }

    public async testRemoveItem(key: string): Promise<void> {
        return this.removeItem(key);
    }

    public async testClearStore(): Promise<void> {
        return this.clearStore();
    }

    public async testKeys(): Promise<string[]> {
        return this.keys();
    }

    public async testLength(): Promise<number> {
        return this.length();
    }

    public async testIsStorageHealthy(): Promise<boolean> {
        return this.isStorageHealthy();
    }

    public async testCleanup(): Promise<number> {
        return this.cleanup();
    }

    public getStorageCapabilities() {
        return super.getStorageCapabilities();
    }
}

describe('RepositoryBase with Storage Abstraction', () => {
    let testRepository: TestRepository;

    beforeEach(async () => {
        // Initialize storage system for testing
        await initializeStorage({
            name: 'test_db',
            version: 1
        });

        testRepository = new TestRepository('test_repo');
        
        // Wait a bit for async initialization
        await new Promise(resolve => setTimeout(resolve, 100));
    });

    afterEach(async () => {
        if (testRepository) {
            await testRepository.testClearStore();
        }
    });

    describe('Basic Operations', () => {
        it('should store and retrieve data correctly', async () => {
            const testData = { message: 'Hello, Repository!', timestamp: Date.now() };
            
            await testRepository.testSetItem('test_key', testData);
            const retrieved = await testRepository.testGetItem('test_key');
            
            expect(retrieved).toEqual(testData);
        });

        it('should handle different data types', async () => {
            const testCases = [
                { key: 'string', value: 'test string' },
                { key: 'number', value: 42 },
                { key: 'boolean', value: true },
                { key: 'array', value: [1, 2, 3] },
                { key: 'object', value: { nested: 'value' } },
                { key: 'null', value: null }
            ];

            // Store all test cases
            for (const testCase of testCases) {
                await testRepository.testSetItem(testCase.key, testCase.value);
            }

            // Retrieve and verify all test cases
            for (const testCase of testCases) {
                const retrieved = await testRepository.testGetItem(testCase.key);
                expect(retrieved).toEqual(testCase.value);
            }
        });

        it('should return null for non-existent keys', async () => {
            const result = await testRepository.testGetItem('non_existent_key');
            expect(result).toBeNull();
        });

        it('should remove items correctly', async () => {
            await testRepository.testSetItem('remove_me', 'test value');
            
            // Verify item exists
            let retrieved = await testRepository.testGetItem('remove_me');
            expect(retrieved).toBe('test value');
            
            // Remove item
            await testRepository.testRemoveItem('remove_me');
            
            // Verify item is gone
            retrieved = await testRepository.testGetItem('remove_me');
            expect(retrieved).toBeNull();
        });

        it('should clear all items', async () => {
            // Add multiple items
            await testRepository.testSetItem('key1', 'value1');
            await testRepository.testSetItem('key2', 'value2');
            await testRepository.testSetItem('key3', 'value3');
            
            // Verify items exist
            let length = await testRepository.testLength();
            expect(length).toBe(3);
            
            // Clear store
            await testRepository.testClearStore();
            
            // Verify store is empty
            length = await testRepository.testLength();
            expect(length).toBe(0);
        });
    });

    describe('Key Management', () => {
        it('should return correct keys', async () => {
            const testKeys = ['key1', 'key2', 'key3'];
            
            // Add items
            for (const key of testKeys) {
                await testRepository.testSetItem(key, `value_${key}`);
            }
            
            // Get keys
            const keys = await testRepository.testKeys();
            
            // Verify all test keys are present
            for (const key of testKeys) {
                expect(keys).toContain(key);
            }
        });

        it('should return correct length', async () => {
            // Initially empty
            let length = await testRepository.testLength();
            expect(length).toBe(0);
            
            // Add items one by one
            await testRepository.testSetItem('key1', 'value1');
            length = await testRepository.testLength();
            expect(length).toBe(1);
            
            await testRepository.testSetItem('key2', 'value2');
            length = await testRepository.testLength();
            expect(length).toBe(2);
            
            await testRepository.testSetItem('key3', 'value3');
            length = await testRepository.testLength();
            expect(length).toBe(3);
        });
    });

    describe('TTL Support', () => {
        it('should handle TTL correctly', async () => {
            const ttlRepository = new TestRepository('ttl_repo', 100); // 100ms TTL
            
            await ttlRepository.testSetItem('ttl_key', 'expires soon');
            
            // Should be available immediately
            let retrieved = await ttlRepository.testGetItem('ttl_key');
            expect(retrieved).toBe('expires soon');
            
            // Wait for expiration
            await new Promise(resolve => setTimeout(resolve, 150));
            
            // Should be expired now
            retrieved = await ttlRepository.testGetItem('ttl_key');
            expect(retrieved).toBeNull();
        });

        it('should cleanup expired items', async () => {
            const ttlRepository = new TestRepository('cleanup_repo', 50); // 50ms TTL
            
            // Add items that will expire
            await ttlRepository.testSetItem('expire1', 'value1');
            await ttlRepository.testSetItem('expire2', 'value2');
            
            // Add item without TTL (by using a different repository)
            const permanentRepo = new TestRepository('cleanup_repo'); // No TTL
            await permanentRepo.testSetItem('permanent', 'permanent_value');
            
            // Wait for expiration
            await new Promise(resolve => setTimeout(resolve, 100));
            
            // Cleanup should remove expired items
            const cleanedCount = await ttlRepository.testCleanup();
            expect(cleanedCount).toBeGreaterThanOrEqual(0);
        });
    });

    describe('Health and Capabilities', () => {
        it('should report storage health', async () => {
            const isHealthy = await testRepository.testIsStorageHealthy();
            expect(isHealthy).toBe(true);
        });

        it('should provide storage capabilities', async () => {
            const capabilities = testRepository.getStorageCapabilities();
            
            if (capabilities) {
                expect(capabilities).toHaveProperty('platform');
                expect(capabilities).toHaveProperty('persistent');
                expect(capabilities).toHaveProperty('supportsTTL');
                expect(capabilities).toHaveProperty('maxStorageSize');
            }
        });
    });

    describe('Error Handling', () => {
        it('should handle storage errors gracefully', async () => {
            // Test with invalid operations
            try {
                await testRepository.testGetItem('');
                // Should either work or return null, but not throw
            } catch (error) {
                // If it throws, should be a meaningful error
                expect(error).toBeInstanceOf(Error);
            }
        });

        it('should handle concurrent operations', async () => {
            const operations = Array.from({ length: 10 }, async (_, i) => {
                await testRepository.testSetItem(`concurrent_${i}`, `value_${i}`);
                return testRepository.testGetItem(`concurrent_${i}`);
            });
            
            const results = await Promise.all(operations);
            
            results.forEach((result, i) => {
                expect(result).toBe(`value_${i}`);
            });
        });
    });

    describe('Repository Isolation', () => {
        it('should isolate data between different repositories', async () => {
            const repo1 = new TestRepository('repo1');
            const repo2 = new TestRepository('repo2');
            
            // Add same key to both repositories
            await repo1.testSetItem('shared_key', 'value_from_repo1');
            await repo2.testSetItem('shared_key', 'value_from_repo2');
            
            // Values should be isolated
            const value1 = await repo1.testGetItem('shared_key');
            const value2 = await repo2.testGetItem('shared_key');
            
            expect(value1).toBe('value_from_repo1');
            expect(value2).toBe('value_from_repo2');
            
            // Clear one repository shouldn't affect the other
            await repo1.testClearStore();
            
            const value1After = await repo1.testGetItem('shared_key');
            const value2After = await repo2.testGetItem('shared_key');
            
            expect(value1After).toBeNull();
            expect(value2After).toBe('value_from_repo2');
        });
    });

    describe('Platform Independence', () => {
        it('should work consistently across different storage adapters', async () => {
            // This test verifies that the repository works the same way
            // regardless of the underlying storage adapter
            
            const testData = {
                string: 'test',
                number: 42,
                boolean: true,
                array: [1, 2, 3],
                object: { nested: 'value' }
            };
            
            await testRepository.testSetItem('platform_test', testData);
            const retrieved = await testRepository.testGetItem('platform_test');
            
            expect(retrieved).toEqual(testData);
            
            // Test that operations work consistently
            const keys = await testRepository.testKeys();
            expect(keys).toContain('platform_test');
            
            const length = await testRepository.testLength();
            expect(length).toBeGreaterThan(0);
        });
    });
});
