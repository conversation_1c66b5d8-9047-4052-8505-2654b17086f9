/**
 * Tests for AuthRepository with storage abstraction.
 * 
 * These tests verify that the AuthRepository works correctly
 * with the new platform-independent storage system.
 */

// Using Jest instead of Vitest
import { AuthRepository } from '../AuthRepository';
import { initializeStorage } from '@/lib/storage';

describe('AuthRepository with Storage Abstraction', () => {
    let authRepository: AuthRepository;

    beforeEach(async () => {
        // Initialize storage system for testing
        await initializeStorage({
            name: 'test_auth_db',
            version: 1
        });

        authRepository = AuthRepository.getInstance();
        
        // Clear any existing data
        await authRepository.clearAuthData();
        
        // Wait a bit for async initialization
        await new Promise(resolve => setTimeout(resolve, 100));
    });

    afterEach(async () => {
        if (authRepository) {
            await authRepository.clearAuthData();
        }
    });

    describe('Token Management', () => {
        it('should store and retrieve access token', async () => {
            const testToken = 'test_access_token_12345';
            
            await authRepository.setAccessToken(testToken);
            const retrieved = await authRepository.getAccessToken();
            
            expect(retrieved).toBe(testToken);
        });

        it('should store and retrieve refresh token', async () => {
            const testToken = 'test_refresh_token_67890';
            
            await authRepository.setRefreshToken(testToken);
            const retrieved = await authRepository.getRefreshToken();
            
            expect(retrieved).toBe(testToken);
        });

        it('should handle null tokens correctly', async () => {
            // Initially should be null
            let accessToken = await authRepository.getAccessToken();
            let refreshToken = await authRepository.getRefreshToken();
            
            expect(accessToken).toBeNull();
            expect(refreshToken).toBeNull();
            
            // Set tokens
            await authRepository.setAccessToken('access');
            await authRepository.setRefreshToken('refresh');
            
            // Should retrieve correctly
            accessToken = await authRepository.getAccessToken();
            refreshToken = await authRepository.getRefreshToken();
            
            expect(accessToken).toBe('access');
            expect(refreshToken).toBe('refresh');
            
            // Clear tokens
            await authRepository.setAccessToken(null);
            await authRepository.setRefreshToken(null);
            
            // Should be null again
            accessToken = await authRepository.getAccessToken();
            refreshToken = await authRepository.getRefreshToken();
            
            expect(accessToken).toBeNull();
            expect(refreshToken).toBeNull();
        });
    });

    describe('User Data Management', () => {
        it('should store and retrieve user data', async () => {
            const testUser = {
                id: '123',
                email: '<EMAIL>',
                name: 'Test User',
                role: 'admin'
            };
            
            await authRepository.setUser(testUser);
            const retrieved = await authRepository.getUser();
            
            expect(retrieved).toEqual(testUser);
        });

        it('should handle complex user objects', async () => {
            const complexUser = {
                id: '456',
                email: '<EMAIL>',
                name: 'Complex User',
                role: 'user',
                profile: {
                    avatar: 'https://example.com/avatar.jpg',
                    preferences: {
                        theme: 'dark',
                        language: 'en',
                        notifications: true
                    }
                },
                permissions: ['read', 'write'],
                lastLogin: new Date().toISOString(),
                metadata: {
                    createdAt: '2023-01-01T00:00:00Z',
                    updatedAt: '2023-12-01T00:00:00Z'
                }
            };
            
            await authRepository.setUser(complexUser);
            const retrieved = await authRepository.getUser();
            
            expect(retrieved).toEqual(complexUser);
        });

        it('should handle null user correctly', async () => {
            // Initially should be null
            let user = await authRepository.getUser();
            expect(user).toBeNull();
            
            // Set user
            const testUser = { id: '789', email: '<EMAIL>', name: 'Test' };
            await authRepository.setUser(testUser);
            
            // Should retrieve correctly
            user = await authRepository.getUser();
            expect(user).toEqual(testUser);
            
            // Clear user
            await authRepository.setUser(null);
            
            // Should be null again
            user = await authRepository.getUser();
            expect(user).toBeNull();
        });
    });

    describe('Authentication State', () => {
        it('should correctly determine authentication status', async () => {
            // Initially not authenticated
            let isAuthenticated = await authRepository.isAuthenticated();
            expect(isAuthenticated).toBe(false);
            
            // Set only access token
            await authRepository.setAccessToken('access_token');
            isAuthenticated = await authRepository.isAuthenticated();
            expect(isAuthenticated).toBe(true);
            
            // Clear access token
            await authRepository.setAccessToken(null);
            isAuthenticated = await authRepository.isAuthenticated();
            expect(isAuthenticated).toBe(false);
            
            // Set both tokens
            await authRepository.setAccessToken('access_token');
            await authRepository.setRefreshToken('refresh_token');
            isAuthenticated = await authRepository.isAuthenticated();
            expect(isAuthenticated).toBe(true);
        });

        it('should handle complete authentication flow', async () => {
            const authData = {
                accessToken: 'new_access_token',
                refreshToken: 'new_refresh_token',
                user: {
                    id: '999',
                    email: '<EMAIL>',
                    name: 'Flow User'
                }
            };
            
            // Set complete auth data
            await authRepository.setAccessToken(authData.accessToken);
            await authRepository.setRefreshToken(authData.refreshToken);
            await authRepository.setUser(authData.user);
            
            // Verify all data is stored correctly
            const isAuthenticated = await authRepository.isAuthenticated();
            const accessToken = await authRepository.getAccessToken();
            const refreshToken = await authRepository.getRefreshToken();
            const user = await authRepository.getUser();
            
            expect(isAuthenticated).toBe(true);
            expect(accessToken).toBe(authData.accessToken);
            expect(refreshToken).toBe(authData.refreshToken);
            expect(user).toEqual(authData.user);
        });
    });

    describe('Data Clearing', () => {
        it('should clear all auth data', async () => {
            // Set all auth data
            await authRepository.setAccessToken('access_token');
            await authRepository.setRefreshToken('refresh_token');
            await authRepository.setUser({ id: '123', email: '<EMAIL>', name: 'Test' });
            
            // Verify data is set
            expect(await authRepository.isAuthenticated()).toBe(true);
            expect(await authRepository.getAccessToken()).toBe('access_token');
            expect(await authRepository.getRefreshToken()).toBe('refresh_token');
            expect(await authRepository.getUser()).not.toBeNull();
            
            // Clear all data
            await authRepository.clearAuthData();
            
            // Verify all data is cleared
            expect(await authRepository.isAuthenticated()).toBe(false);
            expect(await authRepository.getAccessToken()).toBeNull();
            expect(await authRepository.getRefreshToken()).toBeNull();
            expect(await authRepository.getUser()).toBeNull();
        });
    });

    describe('Singleton Pattern', () => {
        it('should maintain singleton instance', () => {
            const instance1 = AuthRepository.getInstance();
            const instance2 = AuthRepository.getInstance();
            
            expect(instance1).toBe(instance2);
        });

        it('should share data across singleton instances', async () => {
            const instance1 = AuthRepository.getInstance();
            const instance2 = AuthRepository.getInstance();
            
            // Set data through instance1
            await instance1.setAccessToken('shared_token');
            await instance1.setUser({ id: '123', email: '<EMAIL>', name: 'Shared' });
            
            // Retrieve data through instance2
            const token = await instance2.getAccessToken();
            const user = await instance2.getUser();
            
            expect(token).toBe('shared_token');
            expect(user).toEqual({ id: '123', email: '<EMAIL>', name: 'Shared' });
        });
    });

    describe('Platform Independence', () => {
        it('should work consistently across different storage adapters', async () => {
            // This test verifies that AuthRepository works the same way
            // regardless of the underlying storage adapter
            
            const authData = {
                accessToken: 'platform_test_access',
                refreshToken: 'platform_test_refresh',
                user: {
                    id: 'platform_123',
                    email: '<EMAIL>',
                    name: 'Platform User',
                    metadata: {
                        complex: true,
                        array: [1, 2, 3],
                        nested: { deep: 'value' }
                    }
                }
            };
            
            // Store auth data
            await authRepository.setAccessToken(authData.accessToken);
            await authRepository.setRefreshToken(authData.refreshToken);
            await authRepository.setUser(authData.user);
            
            // Retrieve and verify
            const isAuthenticated = await authRepository.isAuthenticated();
            const accessToken = await authRepository.getAccessToken();
            const refreshToken = await authRepository.getRefreshToken();
            const user = await authRepository.getUser();
            
            expect(isAuthenticated).toBe(true);
            expect(accessToken).toBe(authData.accessToken);
            expect(refreshToken).toBe(authData.refreshToken);
            expect(user).toEqual(authData.user);
        });

        it('should handle storage errors gracefully', async () => {
            // Test that the repository handles storage errors without crashing
            try {
                // These operations should not throw even if storage has issues
                await authRepository.setAccessToken('test_token');
                const token = await authRepository.getAccessToken();
                
                // Should either work correctly or return null/false
                expect(typeof token === 'string' || token === null).toBe(true);
                
                const isAuth = await authRepository.isAuthenticated();
                expect(typeof isAuth === 'boolean').toBe(true);
                
            } catch (error) {
                // If errors are thrown, they should be meaningful
                expect(error).toBeInstanceOf(Error);
            }
        });
    });

    describe('Performance', () => {
        it('should handle multiple concurrent operations', async () => {
            const operations = Array.from({ length: 10 }, async (_, i) => {
                await authRepository.setAccessToken(`token_${i}`);
                return authRepository.getAccessToken();
            });
            
            const results = await Promise.all(operations);
            
            // The last operation should win
            const finalToken = await authRepository.getAccessToken();
            expect(results).toContain(finalToken);
        });

        it('should handle rapid successive operations', async () => {
            // Rapid set/get operations
            for (let i = 0; i < 10; i++) {
                await authRepository.setAccessToken(`rapid_token_${i}`);
                const token = await authRepository.getAccessToken();
                expect(token).toBe(`rapid_token_${i}`);
            }
        });
    });
});
