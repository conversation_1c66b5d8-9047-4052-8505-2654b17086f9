/**
 * Repository for managing user preferences.
 * 
 * This repository handles the storage of user preferences like language settings
 * using the platform-independent storage abstraction.
 */

import { RepositoryBase } from './RepositoryBase';
import { logger } from '@/lib/logger';

const PREFERRED_LANGUAGE_KEY = 'preferred_language';

export class PreferencesRepository extends RepositoryBase {
    private static instance: PreferencesRepository | null = null;

    /**
     * Get the singleton instance of PreferencesRepository
     */
    public static getInstance(): PreferencesRepository {
        if (!PreferencesRepository.instance) {
            PreferencesRepository.instance = new PreferencesRepository();
        }
        return PreferencesRepository.instance;
    }

    private constructor() {
        // Initialize with store name 'preferences_store' and no TTL (persistent data)
        super('preferences_store');
    }

    /**
     * Save preferred language to storage
     * @param language The language code to save
     */
    public async savePreferredLanguage(language: string): Promise<void> {
        try {
            await this.setItem(PREFERRED_LANGUAGE_KEY, language);
            logger.debug(`PreferencesRepository: Saved preferred language: ${language}`);
        } catch (error) {
            logger.error('PreferencesRepository: Error saving preferred language:', error);
            throw error;
        }
    }

    /**
     * Load preferred language from storage
     * @returns The preferred language code or null if not found
     */
    public async loadPreferredLanguage(): Promise<string | null> {
        try {
            const language = await this.getItem<string>(PREFERRED_LANGUAGE_KEY);
            
            if (!language || language === 'undefined' || language === 'null') {
                return null;
            }

            logger.debug(`PreferencesRepository: Loaded preferred language: ${language}`);
            return language;
        } catch (error) {
            logger.error('PreferencesRepository: Error loading preferred language:', error);
            return null;
        }
    }

    /**
     * Clear all preferences from storage
     */
    public async clearAllPreferences(): Promise<void> {
        try {
            await this.clearStore();
            logger.info('PreferencesRepository: All preferences cleared');
        } catch (error) {
            logger.error('PreferencesRepository: Error clearing preferences:', error);
            throw error;
        }
    }

    /**
     * Migrate language preference from localStorage if it exists (for backward compatibility)
     * @param localStorageKey The localStorage key for language preference
     */
    public async migrateLanguageFromLocalStorage(localStorageKey: string): Promise<void> {
        // Only attempt migration in browser environment
        if (typeof window === 'undefined' || !window.localStorage) {
            return;
        }

        try {
            // Check if we already have a language preference in the new storage
            const existingLanguage = await this.loadPreferredLanguage();
            if (existingLanguage) {
                logger.debug('PreferencesRepository: Language preference already exists, skipping migration');
                return;
            }

            // Try to migrate language preference
            const localStorageLanguage = localStorage.getItem(localStorageKey);
            if (localStorageLanguage && localStorageLanguage !== 'undefined' && localStorageLanguage !== 'null') {
                await this.savePreferredLanguage(localStorageLanguage);
                logger.info(`PreferencesRepository: Migrated language preference from localStorage: ${localStorageLanguage}`);
                
                // Clean up localStorage after successful migration
                localStorage.removeItem(localStorageKey);
                logger.info('PreferencesRepository: Cleaned up localStorage after language migration');
            }

        } catch (error) {
            logger.error('PreferencesRepository: Error during language migration:', error);
            // Don't throw - migration failure shouldn't break the app
        }
    }

    /**
     * Get storage health status
     * @returns True if storage is healthy
     */
    public async isStorageHealthy(): Promise<boolean> {
        try {
            return await super.isStorageHealthy();
        } catch (error) {
            logger.error('PreferencesRepository: Error checking storage health:', error);
            return false;
        }
    }
}

export default PreferencesRepository;
