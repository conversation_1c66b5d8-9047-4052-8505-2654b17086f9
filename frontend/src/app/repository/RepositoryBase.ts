import { logger } from '@/lib/logger';
import type { IStorageAdapter } from '@/lib/storage';
import { getRepositoryStorage } from '@/lib/storage';

/**
 * Base class for repositories, providing a standardized way to interact with
 * platform-independent storage.
 *
 * Each specialized repository that extends this class will operate on its own
 * dedicated store, ensuring data isolation across different platforms
 * (browser, Node.js, React Native, etc.).
 */
export class RepositoryBase {
    private storageAdapter: IStorageAdapter | null = null;
    private readonly storeName: string;
    private readonly ttlInMilliseconds?: number;
    private initialized = false;

    /**
     * Initializes a new instance of the RepositoryBase.
     * @param storeName The unique name for the storage store this repository instance will manage.
     *                  This ensures that data for different repositories is kept separate.
     *                  Example: 'auth_store', 'session_store'.
     * @param ttlInMilliseconds Optional TTL for items stored in this repository
     */
    constructor(storeName: string, ttlInMilliseconds?: number) {
        if (!storeName) {
            throw new Error('RepositoryBase: storeName cannot be empty.');
        }

        this.storeName = storeName;
        this.ttlInMilliseconds = ttlInMilliseconds;

        // Initialize storage asynchronously
        this.initializeStorage().catch(error => {
            logger.error(`RepositoryBase: Failed to initialize storage for ${storeName}:`, error);
        });
    }

    /**
     * Initialize the storage adapter for this repository
     */
    private async initializeStorage(): Promise<void> {
        try {
            this.storageAdapter = await getRepositoryStorage(this.storeName, this.ttlInMilliseconds);
            this.initialized = true;

            if (this.ttlInMilliseconds && this.ttlInMilliseconds > 0) {
                logger.debug(`RepositoryBase initialized for store: ${this.storeName} with TTL: ${this.ttlInMilliseconds}ms`);
            } else {
                logger.debug(`RepositoryBase initialized for store: ${this.storeName} (no TTL)`);
            }
        } catch (error) {
            logger.error(`RepositoryBase: Failed to initialize storage for ${this.storeName}:`, error);
            throw error;
        }
    }

    /**
     * Ensure the storage adapter is initialized before operations
     */
    private async ensureInitialized(): Promise<void> {
        if (!this.initialized || !this.storageAdapter) {
            await this.initializeStorage();
        }
    }

    /**
     * Retrieves an item from the storage.
     * @param key The key of the item to retrieve.
     * @returns A promise that resolves with the item's value, or null if the key is not found or an error occurs.
     */
    protected async getItem<T>(key: string): Promise<T | null> {
        try {
            await this.ensureInitialized();

            if (!this.storageAdapter) {
                logger.error(`RepositoryBase (${this.storeName}): Storage adapter not available`);
                return null;
            }

            // The storage adapter handles TTL internally, so we can directly get the item
            return await this.storageAdapter.getItem<T>(key);
        } catch (error) {
            logger.error(`RepositoryBase (${this.storeName}): Error getting item '${key}':`, error);
            return null;
        }
    }

    /**
     * Sets an item in the storage.
     * @param key The key of the item to set.
     * @param value The value to store.
     * @returns A promise that resolves when the item has been set.
     */
    protected async setItem<T>(key: string, value: T): Promise<void> {
        try {
            await this.ensureInitialized();

            if (!this.storageAdapter) {
                throw new Error(`RepositoryBase (${this.storeName}): Storage adapter not available`);
            }

            // Pass TTL to the storage adapter if configured
            await this.storageAdapter.setItem<T>(key, value, this.ttlInMilliseconds);
        } catch (error) {
            logger.error(`RepositoryBase (${this.storeName}): Error setting item '${key}':`, error);
            throw error;
        }
    }

    /**
     * Removes an item from the storage.
     * @param key The key of the item to remove.
     * @returns A promise that resolves when the item has been removed.
     */
    protected async removeItem(key: string): Promise<void> {
        try {
            await this.ensureInitialized();

            if (!this.storageAdapter) {
                throw new Error(`RepositoryBase (${this.storeName}): Storage adapter not available`);
            }

            await this.storageAdapter.removeItem(key);
        } catch (error) {
            logger.error(`RepositoryBase (${this.storeName}): Error removing item '${key}':`, error);
            throw error;
        }
    }

    /**
     * Clears all items from this repository's specific storage.
     * This does NOT affect other stores within the storage system.
     * @returns A promise that resolves when the store has been cleared.
     */
    protected async clearStore(): Promise<void> {
        try {
            await this.ensureInitialized();

            if (!this.storageAdapter) {
                throw new Error(`RepositoryBase (${this.storeName}): Storage adapter not available`);
            }

            await this.storageAdapter.clear();
            logger.debug(`RepositoryBase (${this.storeName}): Store cleared.`);
        } catch (error) {
            logger.error(`RepositoryBase (${this.storeName}): Error clearing store:`, error);
            throw error;
        }
    }

    /**
     * Gets the number of keys in this repository's specific storage.
     * @returns A promise that resolves with the number of keys.
     */
    protected async length(): Promise<number> {
        try {
            await this.ensureInitialized();

            if (!this.storageAdapter) {
                logger.error(`RepositoryBase (${this.storeName}): Storage adapter not available`);
                return 0;
            }

            return await this.storageAdapter.length();
        } catch (error) {
            logger.error(`RepositoryBase (${this.storeName}): Error getting store length:`, error);
            return 0;
        }
    }

    /**
     * Gets all keys in this repository's specific storage.
     * @returns A promise that resolves with an array of all key names.
     */
    protected async keys(): Promise<string[]> {
        try {
            await this.ensureInitialized();

            if (!this.storageAdapter) {
                logger.error(`RepositoryBase (${this.storeName}): Storage adapter not available`);
                return [];
            }

            return await this.storageAdapter.keys();
        } catch (error) {
            logger.error(`RepositoryBase (${this.storeName}): Error getting all keys:`, error);
            return [];
        }
    }

    /**
     * Check if the storage is healthy and accessible
     * @returns A promise that resolves with true if storage is healthy
     */
    protected async isStorageHealthy(): Promise<boolean> {
        try {
            await this.ensureInitialized();

            if (!this.storageAdapter) {
                return false;
            }

            return await this.storageAdapter.isHealthy();
        } catch (error) {
            logger.error(`RepositoryBase (${this.storeName}): Error checking storage health:`, error);
            return false;
        }
    }

    /**
     * Get storage capabilities for this repository
     * @returns The storage capabilities
     */
    protected getStorageCapabilities() {
        if (!this.storageAdapter) {
            return null;
        }
        return this.storageAdapter.getCapabilities();
    }

    /**
     * Cleanup expired items in this repository's storage
     * @returns A promise that resolves with the number of items cleaned up
     */
    protected async cleanup(): Promise<number> {
        try {
            await this.ensureInitialized();

            if (!this.storageAdapter) {
                logger.error(`RepositoryBase (${this.storeName}): Storage adapter not available`);
                return 0;
            }

            return await this.storageAdapter.cleanup();
        } catch (error) {
            logger.error(`RepositoryBase (${this.storeName}): Error during cleanup:`, error);
            return 0;
        }
    }
}

export default RepositoryBase; 